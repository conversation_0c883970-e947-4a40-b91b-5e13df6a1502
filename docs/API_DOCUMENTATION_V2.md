![<PERSON>ae<PERSON> Logo](../branding/jaeger-logo.png)

# 📚 API Documentation - Version 2.0

**Complete API reference for the Jaeger trading system Version 2.0 (Backtesting-Only Architecture)**

## 🚀 **VERSION 2.0 NEW APIS**

### **1. Backtesting Rule Parser**

#### **Basic Usage:**
```python
from backtesting_rule_parser import parse_backtesting_rules

# Parse LLM response into backtesting functions
rule_functions = parse_backtesting_rules(llm_response)

# Test functions with OHLC data
for func in rule_functions:
    signal = func(ohlc_data, current_index)
    if signal:
        print(f"Signal: {signal}")
```

#### **Advanced Usage:**
```python
from backtesting_rule_parser import BacktestingRuleParser

# Create parser instance
parser = BacktestingRuleParser()

# Parse LLM response into rule objects
rules = parser.parse_llm_response(llm_response)

# Generate Python functions
functions = parser.generate_python_functions()

# Access individual rules
for rule in rules:
    print(f"Rule: {rule.name}")
    print(f"Entry: {rule.entry_logic}")
    print(f"Direction: {rule.direction}")
```

#### **Rule Object Structure:**
```python
@dataclass
class BacktestingTradingRule:
    rule_id: int
    name: str
    market_logic: str
    entry_logic: str
    direction: str  # 'long' or 'short'
    stop_logic: str
    target_logic: str
    position_size: float
    timeframe: str = "5min"
```

### **2. Walk-Forward Validator**

#### **Basic Usage:**
```python
from backtesting_walk_forward_validator import validate_backtesting_patterns

# Validate patterns with walk-forward analysis
validation_results = validate_backtesting_patterns(llm_response, ohlc_data)

if validation_results['success']:
    profitable_patterns = validation_results['profitable_patterns']
    success_rate = validation_results['success_rate']
    print(f"Found {len(profitable_patterns)} profitable patterns ({success_rate:.1f}% success)")
```

#### **Advanced Usage:**
```python
from backtesting_walk_forward_validator import BacktestingWalkForwardValidator

# Create validator with custom thresholds
validator = BacktestingWalkForwardValidator(
    min_return_threshold=5.0,
    min_consistency_score=60.0,
    min_win_rate=45.0,
    max_drawdown_threshold=15.0
)

# Validate patterns
validation_results = validator.validate_patterns(llm_response, ohlc_data)

# Generate validation report
report = validator.generate_validation_report(validation_results)
print(report)
```

#### **Validation Results Structure:**
```python
{
    'success': bool,
    'total_patterns': int,
    'profitable_patterns': List[BacktestingTradingRule],
    'validation_results': Dict[int, Dict],
    'success_rate': float
}
```

### **3. Hard-coded MT4 Converter**

#### **Basic Usage:**
```python
from hardcoded_mt4_converter import convert_profitable_patterns_to_mt4

# Convert validated patterns to MT4 EA
mt4_ea_code = convert_profitable_patterns_to_mt4(profitable_patterns, "My_EA")

# Save EA file
with open("My_EA.mq4", "w") as f:
    f.write(mt4_ea_code)
```

#### **Advanced Usage:**
```python
from hardcoded_mt4_converter import HardcodedMT4Converter

# Create converter instance
converter = HardcodedMT4Converter()

# Convert rules to MT4
mt4_code = converter.convert_rules_to_mt4(profitable_rules, "Advanced_EA")

# Check for conversion errors
if converter.conversion_errors:
    print("Conversion errors:", converter.conversion_errors)
```

### **4. Simplified LLM Prompts**

#### **Basic Usage:**
```python
from ai_integration.situational_prompts_backtesting import BacktestingOnlyPrompts

# Generate backtesting-only prompt
prompt = BacktestingOnlyPrompts.generate_backtesting_discovery_prompt(
    ohlc_data=data,
    market_summaries="Market analysis summary",
    performance_feedback="Previous pattern performance"
)

# Send to LLM
llm_response = your_llm_client.send_prompt(prompt)
```

#### **Advanced Usage:**
```python
# Get core situational questions
questions = BacktestingOnlyPrompts.get_core_situational_questions()

# Get Tom Hougaard examples
examples = BacktestingOnlyPrompts.get_tom_hougaard_examples()

# Custom prompt generation with market regime analysis
prompt = BacktestingOnlyPrompts.generate_backtesting_discovery_prompt(
    ohlc_data=data,
    profit_context="Focus on high-probability setups",
    market_summaries=behavioral_analysis,
    performance_feedback=learning_data
)
```

## 🔄 **INTEGRATION WORKFLOW**

### **Complete v2.0 Pipeline:**
```python
# 1. Generate simplified prompt
from ai_integration.situational_prompts_backtesting import BacktestingOnlyPrompts
prompt = BacktestingOnlyPrompts.generate_backtesting_discovery_prompt(ohlc_data, summaries, feedback)

# 2. Get LLM response (your LLM integration)
llm_response = your_llm.generate(prompt)

# 3. Parse into backtesting functions
from backtesting_rule_parser import parse_backtesting_rules
rule_functions = parse_backtesting_rules(llm_response)

# 4. Validate with walk-forward analysis
from backtesting_walk_forward_validator import validate_backtesting_patterns
validation_results = validate_backtesting_patterns(llm_response, ohlc_data)

# 5. Convert profitable patterns to MT4
from hardcoded_mt4_converter import convert_profitable_patterns_to_mt4
if validation_results['success']:
    profitable_patterns = validation_results['profitable_patterns']
    mt4_ea_code = convert_profitable_patterns_to_mt4(profitable_patterns, "Validated_EA")

# 6. Generate files (existing file_generator.py integration)
from file_generator import FileGenerator
file_gen = FileGenerator()
generated_files = file_gen.generate_trading_system_files(
    cortex_results, backtest_results, validation_results
)
```

## 🛠️ **MIGRATION FROM V1.0**

### **For Existing Code:**
- **No changes required** - Cortex usage remains identical
- **Improved reliability** - Same interface, better results
- **Enhanced outputs** - Better MT4 EAs and validation

### **For Custom Integrations:**
- **Replace**: `llm_rule_parser.py` → `backtesting_rule_parser.py`
- **Add**: Walk-forward validation step
- **Update**: MT4 generation to use hard-coded converter
- **Simplify**: LLM prompts to backtesting-only format

## 🧪 **TESTING APIS**

### **Test Utilities:**
```python
# Test parsing reliability
from test_backtesting_only import test_backtesting_parser
success_rate = test_backtesting_parser()

# Test complete refactoring benefits
from test_refactoring_benefits import run_comprehensive_test
overall_success = run_comprehensive_test()

# Debug parser issues
from debug_backtesting_parser import run_debug_tests
debug_results = run_debug_tests()
```

## 📊 **PERFORMANCE MONITORING**

### **Success Metrics:**
```python
# Track parsing success rates
parsing_success = len(parsed_rules) / total_patterns * 100

# Monitor signal generation rates
signal_rate = signals_found / total_tests * 100

# Measure MT4 conversion reliability
mt4_reliability = successful_conversions / total_conversions * 100

# Validate walk-forward success
wf_success = profitable_patterns / total_patterns * 100
```

## 🎯 **BEST PRACTICES**

### **1. Pattern Validation:**
- Always use walk-forward validation before MT4 deployment
- Set appropriate profitability thresholds for your strategy
- Monitor pattern performance over time

### **2. Error Handling:**
- Check validation results before proceeding to MT4 conversion
- Handle parsing errors gracefully
- Log conversion issues for debugging

### **3. Performance Optimization:**
- Use appropriate walk-forward splits for your data size
- Adjust validation thresholds based on market conditions
- Monitor system performance metrics regularly

---

**📋 For complete examples and integration guides, see:**
- [REFACTORING_REPORT.md](../REFACTORING_REPORT.md)
- [TECHNICAL_DOCUMENTATION.md](TECHNICAL_DOCUMENTATION.md)
- [USER_DOCUMENTATION.md](USER_DOCUMENTATION.md)
