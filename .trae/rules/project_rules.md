# Jaeger Trading System - Core Principles & Rules

## Core Quality Standards & Principles

- User strongly prefers simple, direct solutions without overengineering, wrapper classes, or unnecessary complexity.
- User requires all modules to achieve 90%+ test coverage with all tests passing and warnings fixed as the quality standard.
- User requires comprehensive project cleanup including removal of dead code, unused imports/classes, duplicates, redundancies, hardcoded values, todos, stubs, and unneeded files from project root.
- User requires 100% consistent capitalization of Open, High, Low, Close columns across the entire project.
- User prefers clear and understandable output messages from the system.

## UNBREAKABLE RULES

### 1. ZERO FALLBACKS PRINCIPLE

- **UNBREAKABLE RULE**: Jaeger system must have ZERO fallbacks anywhere - system must either work exactly as LLM intended or fail completely, as fallbacks cause catastrophic damage to the project.
- System must fail fast and loud rather than use any default values, approximations, or alternative logic paths.
- Better to not trade than trade incorrectly - financial accuracy is non-negotiable.

### 2. REAL DATA ONLY RULE

- **UNBREAKABLE RULE**: Never use synthetic data in tests - all tests must use real market data from /tests/RealTestData folder.
- No np.random data generation, artificial OHLC creation, mock market data, or any fabricated trading scenarios.
- Tests must raise FileNotFoundError with "UNBREAKABLE RULE VIOLATION" if real data is missing.

## System Architecture & Component Responsibilities

### Core Architecture

- **Cortex** (cortex.py): Central AI orchestrator that ONLY coordinates LLM communication and orchestration, not backtesting execution, behavioral analysis, trade extraction, or file generation.
- **backtesting.py**: Professional backtesting framework that handles ALL data ingestion, processing, and timeframe generation, with Cortex only coordinating LLM analysis on processed data.

### Current System Architecture (Version 2.0)

The system uses a **single-format approach**: LLM generates patterns in backtesting-compatible format, which are then converted to MT4 using deterministic (non-LLM) conversion.

**Data Flow**: `LLM → Backtesting Parser → Walk-Forward Validation → Hard-coded MT4 Conversion → File Generation`

**Key Components**:

- `backtesting_rule_parser.py`: Parses LLM responses into backtesting-compatible Python functions
- `mt4_hardcoded_converter.py`: Deterministically converts validated backtesting rules to MT4 EA code (no LLM involved)
- `backtesting_walk_forward_validator.py`: Validates patterns using professional walk-forward testing

### Configuration Management

- **NO hardcoded parameters anywhere** - all parameters must be in configuration file (config.py and jaeger_config.env).
- Always use 'llm_env' virtual environment for all Python commands in the Jaeger project.
- All file paths auto-detected from project root with absolute path resolution.

## File Organization & Naming Conventions

- Branding guide and documentation in /docs directory with Jaeger logo at the top of all files.
- Jaeger results files (.md, .mq4, .csv, .html) organized in folders named by symbol rather than timestamps.
- 'Gipsy Danger' naming for MT4 Expert Advisors with sequential numbering (001, 002, etc.) without timestamps.
- LLM learning data stored in /llm_data/SYMBOL/ directory structure, keeping last 100 sessions for learning.
- Backup files saved to /Users/<USER>/JaegerBackups directory with simple backup structure.

## Trading Strategy & Analysis Approach

### Core Methodology

- Jaeger system uses Tom Hougaard's 'situational analysis' methodology (not technical or fundamental analysis).
- LLM must discover sophisticated multi-timeframe patterns that include behavioral context combined with breakout conditions.
- All trading rules must be implementable as MT4 Expert Advisors, which creates a clear boundary for rule complexity.

### Trading System Configuration

- **Multi-timeframe Analysis**: LLM examines all timeframes from 5-minute upwards (not limited to single timeframe).
- **Profitability Focus**: LLM must find PROFITABLE patterns (not just any patterns) and use /llm_data directory for learning from previous sessions.
- **Account Settings**: Initial account balance of $100,000 for backtesting configuration.
- **Complete Rule Definition**: LLM must define ALL trading rules including entry, exit, stop loss, and take profit levels with no hard-coded fallbacks.
- **Trading Costs**: Realistic spread (1 pip) but no commission costs in backtesting.
- **Directional Trading**: System finds both LONG and SHORT trades, not just LONG-only patterns.
- **Risk Management**: LLM prompts include 2-pip padding for stop loss distances to account for spread.
- **Pattern Guidelines**: LLM should avoid hard-coded day-of-week rules (use as examples only for situational context).

### Behavioral Intelligence & Learning System

- **Multi-dimensional Analysis**: System generates 140+ behavioral metrics across 7 timeframes (5min, 15min, 1h, 4h, 1d, 1w, 1M).
- **Session Learning**: Loads last 100 analysis sessions from /llm_data/SYMBOL/ directory for LLM learning and pattern improvement.
- **Contextual Analysis**: Provides rich behavioral context to LLM including market regime, volatility patterns, and participant behavior.
- **Situational Examples**: Uses authentic Tom Hougaard situational analysis examples like "If Thursday is higher than Friday, then what does the following Monday look like?" to guide LLM thinking.

## LLM Pattern Format & Testing

- **Pattern Structure**: LM Studio (local LLM) generates trading patterns in **PATTERN X:** format with specific required fields (Market Logic, Entry Logic, Direction, Stop Logic, Target Logic, Position Size, Timeframe).
- **Individual Testing**: Each LLM pattern tested separately with complete rule sets (entry, exit, initial stop, trailing stop) using exact LLM-recommended instructions and timeframes.
- **Output Preferences**: Professional backtesting.py HTML charts preferred over manual metrics, with comprehensive metrics in .md files including Equity, Drawdown, and Balance with quarterly/monthly/weekly averages.
- **File Generation**: High-quality MT4 Expert Advisor (.mq4) file generation and comprehensive .md metrics matching backtesting.py output required.

## System Execution Requirements

- **Reliability Standards**: System must achieve 100% pattern parsing success, 100% signal generation rate, and 100% MT4 conversion reliability (no partial failures allowed).
- **Order Execution**: System needs capability for market orders (vs limit orders) for both backtesting.py framework and MT4 EA implementation.
- **Realistic Timing**: MT4-like execution with `trade_on_close=True` parameter for realistic market order timing simulation.
- **Profitability Filter**: Only profitable patterns (minimum 2:1 risk-reward ratio) advance from backtesting validation to MT4 file generation.

## Development Standards

- Use task management tools for complex multi-step work that benefits from structured planning.
- Always gather comprehensive information before making code edits using codebase-retrieval tool.
- Focus on following user instructions exactly - ask before carrying out actions beyond user's request.
- If repeatedly calling tools without progress, ask user for help.
- Always suggest writing/updating tests after code changes to verify correctness.
