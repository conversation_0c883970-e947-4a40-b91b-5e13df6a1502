import os
import pytest
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
from ai_integration import lm_studio_client

def test_main_branches(monkeypatch, capsys):
    # Mock sys.argv to avoid argparse conflicts with pytest
    monkeypatch.setattr(sys, "argv", ["lm_studio_client.py"])
    monkeypatch.setattr(sys, "exit", lambda code=0: (_ for _ in ()).throw(SystemExit(code)))
    # Patch LMStudioClient methods for server not running
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "is_server_running", lambda self: False)
    with pytest.raises(SystemExit) as e:
        lm_studio_client.main()
    assert e.value.code == 1
    out = capsys.readouterr().out
    assert "not running" in out
    # Patch LMStudioClient methods for server running, no working model
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "is_server_running", lambda self: True)
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "get_available_models", lambda self: {"data": []})
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "get_working_model", lambda self: None)
    with pytest.raises(SystemExit) as e:
        lm_studio_client.main()
    assert e.value.code == 1
    out = capsys.readouterr().out
    assert "No models available" in out
    # Patch LMStudioClient for working model, but test_model_loading fails
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "get_available_models", lambda self: {"data": [{"id": "test-model", "object": "model"}]})
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "get_display_name", lambda self, model: "Test Model")
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "test_model_loading", lambda self, model: False)
    with pytest.raises(SystemExit) as e:
        lm_studio_client.main()
    assert e.value.code == 1
    out = capsys.readouterr().out
    assert "failed to load" in out
    # Patch LMStudioClient for full success
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "test_model_loading", lambda self, model: True)
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "send_message", lambda self, *a, **k: {"response": "ok"})
    with pytest.raises(SystemExit) as e:
        lm_studio_client.main()
    assert e.value.code == 0
    out = capsys.readouterr().out
    assert "AI communication working" in out
