import os
import pytest
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
from ai_integration import lm_studio_client

def test_main_branches(monkeypatch, capsys):
    monkeypatch.setattr(sys, "exit", lambda code=0: (_ for _ in ()).throw(SystemExit(code)))
    # Patch LMStudioClient methods for server not running
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "is_server_running", lambda self: False)
    with pytest.raises(SystemExit) as e:
        lm_studio_client.main()
    assert e.value.code == 1
    out = capsys.readouterr().out
    assert "not running" in out
    # Patch LMStudioClient methods for server running, no working model
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "is_server_running", lambda self: True)
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "get_available_models", lambda self: {"data": []})
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "get_working_model", lambda self: None)
    with pytest.raises(SystemExit) as e:
        lm_studio_client.main()
    assert e.value.code == 1
    out = capsys.readouterr().out
    assert "No working models found" in out
    # Patch LMStudioClient for working model, but send_message returns error
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "get_working_model", lambda self: "real-model")
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "send_message", lambda self, *a, **k: {"error": "fail"})
    with pytest.raises(SystemExit) as e:
        lm_studio_client.main()
    assert e.value.code == 1
    out = capsys.readouterr().out
    assert "Error" in out
    # Patch LMStudioClient for full success
    monkeypatch.setattr(lm_studio_client.LMStudioClient, "send_message", lambda self, *a, **k: {"response": "ok"})
    with pytest.raises(SystemExit) as e:
        lm_studio_client.main()
    assert e.value.code == 0
    out = capsys.readouterr().out
    assert "AI communication working" in out
