import unittest
import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from ai_integration.lm_studio_client import LMStudioClient

class TestLMStudioClient(unittest.TestCase):
    def test_class_instantiation(self):
        try:
            client = LMStudioClient("http://localhost:1234")
        except Exception as e:
            self.fail(f"LMStudioClient failed to instantiate: {e}")

    def test_no_hardcoded_params(self):
        path = os.path.join(os.path.dirname(__file__), '../src/ai_integration/lm_studio_client.py')
        with open(path, 'r') as f:
            content = f.read()
        self.assertNotIn('="', content, msg='UNBREAKABLE RULE VIOLATION: Hardcoded parameter found in lm_studio_client.py')

    def test_is_server_running_failfast(self):
        client = LMStudioClient("http://localhost:1234")
        with self.assertRaises(RuntimeError) as ctx:
            client.is_server_running()
        self.assertIn('FAIL HARD', str(ctx.exception), 'UNBREAKABLE RULE VIOLATION: Fallback or default logic used in is_server_running')

    def test_get_available_models_failfast(self):
        client = LMStudioClient("http://localhost:1234")
        with self.assertRaises(RuntimeError) as ctx:
            client.get_available_models()
        self.assertIn('FAIL HARD', str(ctx.exception), 'UNBREAKABLE RULE VIOLATION: Fallback or default logic used in get_available_models')

    def test_model_loading_failfast(self):
        client = LMStudioClient("http://localhost:1234")
        result = client.test_model_loading("fake-model")
        self.assertFalse(result, 'UNBREAKABLE RULE VIOLATION: test_model_loading must not fallback or succeed with fake input')

    def test_get_working_model_failfast(self):
        client = LMStudioClient("http://localhost:1234")
        if hasattr(client, 'get_working_model'):
            with self.assertRaises(RuntimeError) as ctx:
                client.get_working_model()
            self.assertIn('FAIL HARD', str(ctx.exception), 'UNBREAKABLE RULE VIOLATION: Fallback or default logic used in get_working_model')

    def test_send_message_failfast(self):
        client = LMStudioClient("http://localhost:1234")
        if hasattr(client, 'send_message'):
            with self.assertRaises(RuntimeError) as ctx:
                client.send_message("test", "fake-model", 0.7, 10)
            self.assertIn('FAIL HARD', str(ctx.exception), 'UNBREAKABLE RULE VIOLATION: Fallback or default logic used in send_message')

    def test_main_function_exists(self):
        self.assertTrue(hasattr(LMStudioClient, 'main') or hasattr(LMStudioClient, '__call__'), 'UNBREAKABLE RULE VIOLATION: main or __call__ missing')

if __name__ == '__main__':
    unittest.main()
