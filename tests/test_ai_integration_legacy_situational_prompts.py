import unittest
import os
import sys
import pandas as pd
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from ai_integration.legacy_situational_prompts import SituationalAnalysisPrompts

REAL_DATA_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_500_bars.csv'))
REQUIRED_COLS = ['Open', 'High', 'Low', 'Close', 'Volume']

class TestSituationalAnalysisPrompts(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        if not os.path.exists(REAL_DATA_PATH):
            raise FileNotFoundError('UNBREAKABLE RULE VIOLATION: Real market data missing at {}'.format(REAL_DATA_PATH))
        cls.df = pd.read_csv(REAL_DATA_PATH)
        # Enforce strict OHLCV capitalization
        for col in REQUIRED_COLS:
            if col not in cls.df.columns:
                raise AssertionError(f"UNBREAKABLE RULE VIOLATION: Missing required column: {col}")
        # No lower/mixed-case OHLCV
        for col in cls.df.columns:
            if col.lower() in ['open', 'high', 'low', 'close', 'volume']:
                assert col in REQUIRED_COLS, f"UNBREAKABLE RULE VIOLATION: Non-strict OHLCV capitalization: {col}"

    def test_get_core_situational_questions(self):
        questions = SituationalAnalysisPrompts.get_core_situational_questions()
        self.assertIsInstance(questions, list)
        self.assertGreater(len(questions), 0)
        for q in questions:
            self.assertIsInstance(q, str)
            self.assertTrue(len(q) > 0)

    def test_get_tom_hougaard_examples(self):
        examples = SituationalAnalysisPrompts.get_tom_hougaard_examples()
        self.assertIsInstance(examples, list)
        self.assertGreater(len(examples), 0)
        for e in examples:
            self.assertIsInstance(e, str)
            self.assertTrue(len(e) > 0)

    def test_get_situational_categories(self):
        categories = SituationalAnalysisPrompts.get_situational_categories()
        self.assertIsInstance(categories, dict)
        self.assertIn('temporal_situations', categories)
        self.assertIn('participant_behavioral', categories)
        self.assertIn('volatility_based', categories)
        self.assertIn('market_structure', categories)
        self.assertIn('risk_management', categories)

    def test_generate_situational_discovery_prompt_real_data(self):
        # Use only real data, fail fast if missing
        df = self.df.copy()
        profit_context = "Profit context example"
        market_summaries = "Market summary example"
        performance_feedback = "Performance feedback example"
        prompt = SituationalAnalysisPrompts.generate_situational_discovery_prompt(
            df, profit_context, market_summaries, performance_feedback
        )
        self.assertIsInstance(prompt, str)
        self.assertIn('pattern', prompt.lower())
        self.assertIn('MT4', prompt)
        self.assertIn('PROFITABILITY REQUIREMENT', prompt)

    def test_generate_situational_validation_prompt_real_data(self):
        df = self.df.copy()
        discovered_patterns = "Pattern1: ... Pattern2: ..."
        # Add datetime column for validation context
        df['datetime'] = pd.date_range(start='2023-01-01', periods=len(df), freq='T')
        prompt = SituationalAnalysisPrompts.generate_situational_validation_prompt(discovered_patterns, df)
        self.assertIsInstance(prompt, str)
        self.assertIn('VALIDATION CRITERIA', prompt)
        self.assertIn('DISCOVERED PATTERNS', prompt)
        self.assertIn('MARKET DATA CONTEXT', prompt)

    def test_analyze_market_regime(self):
        df = self.df.copy()
        result = SituationalAnalysisPrompts._analyze_market_regime(df)
        self.assertIsInstance(result, str)
        self.assertTrue('Market Regime' in result or 'unavailable' in result)

    def test_no_hardcoded_params(self):
        path = os.path.join(os.path.dirname(__file__), '../src/ai_integration/legacy_situational_prompts.py')
        with open(path, 'r') as f:
            content = f.read()
        self.assertNotIn('="', content, msg='UNBREAKABLE RULE VIOLATION: Hardcoded parameter found in legacy_situational_prompts.py')

    def test_fail_fast_on_missing_data(self):
        missing_path = os.path.abspath(os.path.join(os.path.dirname(__file__), 'RealTestData', 'missing.csv'))
        if os.path.exists(missing_path):
            os.remove(missing_path)
        with self.assertRaises(FileNotFoundError):
            pd.read_csv(missing_path)

if __name__ == '__main__':
    unittest.main()
