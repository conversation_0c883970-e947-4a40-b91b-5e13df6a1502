import os
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
import pytest
from unittest.mock import patch
import requests
from ai_integration.lm_studio_client import LMStudioClient

REAL_DATA_PATH = os.path.join(os.path.dirname(__file__), 'RealTestData')

def _require_real_data():
    if not os.path.exists(REAL_DATA_PATH):
        raise FileNotFoundError('UNBREAKABLE RULE VIOLATION: Real market data required for all tests.')

class TestLMStudioClientJaegerCompliance:
    def setup_method(self):
        _require_real_data()
        self.base_url = "http://localhost:1234"  # Example, not used for real connection
        self.client = LMStudioClient(self.base_url)

    @patch('ai_integration.lm_studio_client.requests.get')
    def test_is_server_running_fail_fast(self, mock_get):
        # Mock connection failure to simulate LM Studio not running
        mock_get.side_effect = requests.exceptions.ConnectionError("Connection refused")
        # Should raise RuntimeError due to connection fail (no fallback allowed)
        with pytest.raises(RuntimeError, match=r"FAIL HARD: LM Studio connection failed"):
            self.client.is_server_running()

    @patch('ai_integration.lm_studio_client.requests.get')
    def test_get_available_models_fail_fast(self, mock_get):
        # Mock connection failure to simulate LM Studio not running
        mock_get.side_effect = requests.exceptions.ConnectionError("Connection refused")
        # Should raise RuntimeError (no fallback allowed)
        with pytest.raises(RuntimeError, match="FAIL HARD: LM Studio connection failed"):
            self.client.get_available_models()

    def test_test_model_loading_fake_model(self):
        # Should always return False for fake model
        assert self.client.test_model_loading("fake-model") is False

    def test_test_model_loading_fail_paths(self, monkeypatch):
        import requests
        # Patch requests.post to return status_code != 200
        class Resp:
            status_code = 500
            text = "fail"
            def json(self):
                return {}
        monkeypatch.setattr(requests, "post", lambda *a, **k: Resp())
        with pytest.raises(RuntimeError, match="FAIL HARD: LM Studio API Error: 500 - fail"):
            self.client.test_model_loading("real-model")
        # Patch requests.post to return status_code=200 but missing choices
        class Resp2:
            status_code = 200
            def json(self):
                return {}
        monkeypatch.setattr(requests, "post", lambda *a, **k: Resp2())
        with pytest.raises(RuntimeError, match="FAIL HARD: Malformed LM Studio response"):
            self.client.test_model_loading("real-model")
        # Patch requests.post to raise Exception
        monkeypatch.setattr(requests, "post", lambda *a, **k: (_ for _ in ()).throw(Exception("fail")))
        with pytest.raises(RuntimeError, match="FAIL HARD: Cannot test model loading"):
            self.client.test_model_loading("real-model")

    def test_test_model_loading_success(self, monkeypatch):
        import requests
        # Patch requests.post to return success
        class Resp:
            status_code = 200
            def json(self):
                return {"choices": ["ok"]}
        monkeypatch.setattr(requests, "post", lambda *a, **k: Resp())
        assert self.client.test_model_loading("real-model") is True

    def test_fail_on_missing_real_data(self, monkeypatch):
        monkeypatch.setattr(os.path, "exists", lambda path: False)
        with pytest.raises(FileNotFoundError, match="UNBREAKABLE RULE VIOLATION"):
            _require_real_data()

    def test_get_working_model_fail_fast(self, monkeypatch):
        # Patch get_available_models to always raise
        monkeypatch.setattr(self.client, "get_available_models", lambda: (_ for _ in ()).throw(RuntimeError("fail")))
        with pytest.raises(RuntimeError, match="FAIL HARD: No models available"):
            self.client.get_working_model()
        # Patch get_available_models to return no data
        monkeypatch.setattr(self.client, "get_available_models", lambda: {})
        with pytest.raises(RuntimeError, match="FAIL HARD: No models found"):
            self.client.get_working_model()
        # Patch get_available_models to return only embedding models
        monkeypatch.setattr(self.client, "get_available_models", lambda: {"data": [{"id": "embedding-model"}]})
        with pytest.raises(RuntimeError, match="FAIL HARD: No working models found"):
            self.client.get_working_model()

    def test_send_message_fail_fast(self, monkeypatch):
        # Patch is_server_running to always fail
        monkeypatch.setattr(self.client, "is_server_running", lambda: False)
        with pytest.raises(RuntimeError, match="FAIL HARD: LM Studio server is not running"):
            self.client.send_message("msg", "model", 0.1, 10)
        # Patch is_server_running to succeed, model to None, get_working_model to fail
        monkeypatch.setattr(self.client, "is_server_running", lambda: True)
        monkeypatch.setattr(self.client, "get_working_model", lambda: None)
        with pytest.raises(RuntimeError, match="FAIL HARD: No working models found"):
            self.client.send_message("msg", None, 0.1, 10)
        # Patch get_working_model to return a model, patch requests.post to raise RequestException
        monkeypatch.setattr(self.client, "get_working_model", lambda: "real-model")
        import requests
        monkeypatch.setattr(requests, "post", lambda *a, **k: (_ for _ in ()).throw(requests.exceptions.RequestException("fail")))
        with pytest.raises(RuntimeError, match="FAIL HARD: LM Studio connection failed"):
            self.client.send_message("msg", "real-model", 0.1, 10)
        # Patch requests.post to raise generic Exception
        monkeypatch.setattr(requests, "post", lambda *a, **k: (_ for _ in ()).throw(Exception("fail")))
        with pytest.raises(RuntimeError, match="FAIL HARD: LM Studio communication error"):
            self.client.send_message("msg", "real-model", 0.1, 10)
        # Patch requests.post to return status_code != 200
        class Resp:
            status_code = 500
            text = "fail"
            def json(self):
                return {}
        monkeypatch.setattr(requests, "post", lambda *a, **k: Resp())
        with pytest.raises(RuntimeError, match="FAIL HARD: LM Studio API Error: 500 - fail"):
            self.client.send_message("msg", "real-model", 0.1, 10)
        # Patch requests.post to return status_code=200 but missing choices
        class Resp2:
            status_code = 200
            def json(self):
                return {}
        monkeypatch.setattr(requests, "post", lambda *a, **k: Resp2())
        with pytest.raises(RuntimeError, match="FAIL HARD: Malformed LM Studio response"):
            self.client.send_message("msg", "real-model", 0.1, 10)
        # Patch requests.post to return success
        class Resp3:
            status_code = 200
            def json(self):
                return {"choices": [{"message": {"content": "ok"}}], "model": "real-model", "usage": {"tokens": 1}}
        monkeypatch.setattr(requests, "post", lambda *a, **k: Resp3())
        assert self.client.send_message("msg", "real-model", 0.1, 10)["response"] == "ok"

    def test_get_working_model_success_path(self, monkeypatch):
        # Test successful path with working model found
        mock_models = {
            "data": [
                {"id": "embedding-model"},  # Should be skipped
                {"id": "chat-model"}  # Should be tested
            ]
        }
        monkeypatch.setattr(self.client, "get_available_models", lambda: mock_models)
        monkeypatch.setattr(self.client, "test_model_loading", lambda model_id: model_id == "chat-model")
        
        # Mock print to capture output
        printed_output = []
        monkeypatch.setattr("builtins.print", lambda *args: printed_output.append(" ".join(str(arg) for arg in args)))
        
        result = self.client.get_working_model()
        assert result == "chat-model"
        assert any("Testing model: chat-model" in output for output in printed_output)
        assert any("Model chat-model is working!" in output for output in printed_output)

    def test_get_working_model_all_models_fail(self, monkeypatch):
        # Test when all models fail to load
        mock_models = {
            "data": [
                {"id": "failing-model-1"},
                {"id": "failing-model-2"}
            ]
        }
        monkeypatch.setattr(self.client, "get_available_models", lambda: mock_models)
        monkeypatch.setattr(self.client, "test_model_loading", lambda model_id: False)
        
        # Mock print to capture output
        printed_output = []
        monkeypatch.setattr("builtins.print", lambda *args: printed_output.append(" ".join(str(arg) for arg in args)))
        
        with pytest.raises(RuntimeError, match="FAIL HARD: No working models found"):
            self.client.get_working_model()
        
        assert any("failed to load" in output for output in printed_output)

    def test_client_initialization(self):
        # Test client initialization with different base URLs
        client1 = LMStudioClient("http://localhost:1234")
        assert client1.base_url == "http://localhost:1234"
        assert client1.headers == {"Content-Type": "application/json"}
        
        client2 = LMStudioClient("http://custom-host:5678")
        assert client2.base_url == "http://custom-host:5678"
        assert client2.headers == {"Content-Type": "application/json"}

    def test_send_message_with_model_specified(self, monkeypatch):
        # Test send_message when model is already specified (skip get_working_model)
        monkeypatch.setattr(self.client, "is_server_running", lambda: True)
        
        import requests
        class SuccessResp:
            status_code = 200
            def json(self):
                return {
                    "choices": [{"message": {"content": "test response"}}],
                    "model": "specified-model",
                    "usage": {"total_tokens": 50}
                }
        
        monkeypatch.setattr(requests, "post", lambda *a, **k: SuccessResp())
        
        result = self.client.send_message("test message", "specified-model", 0.7, 100)
        assert result["response"] == "test response"
        assert result["model"] == "specified-model"
        assert result["usage"]["total_tokens"] == 50

    def test_test_model_loading_with_empty_choices(self, monkeypatch):
        # Test when API returns 200 but choices array is empty
        import requests
        class EmptyChoicesResp:
            status_code = 200
            def json(self):
                return {"choices": []}
        
        monkeypatch.setattr(requests, "post", lambda *a, **k: EmptyChoicesResp())
        
        with pytest.raises(RuntimeError, match="FAIL HARD: Malformed LM Studio response"):
            self.client.test_model_loading("test-model")

    def test_get_available_models_different_status_codes(self, monkeypatch):
        # Test different HTTP status codes for get_available_models
        import requests
        
        # Test 404 status code
        class NotFoundResp:
            status_code = 404
            text = "Not Found"
        
        monkeypatch.setattr(requests, "get", lambda *a, **k: NotFoundResp())
        
        with pytest.raises(RuntimeError, match="FAIL HARD: LM Studio API Error: 404 - Not Found"):
            self.client.get_available_models()

    def test_is_server_running_different_scenarios(self, monkeypatch):
        # Test different scenarios for is_server_running
        import requests

        # Test when server returns non-200 status (should fail hard)
        class ErrorResp:
            status_code = 500
            text = "Internal Server Error"

        monkeypatch.setattr(requests, "get", lambda *a, **k: ErrorResp())

        with pytest.raises(RuntimeError, match=r"FAIL HARD: LM Studio API Error: 500"):
            self.client.is_server_running()

        # Test timeout error specifically
        monkeypatch.setattr(requests, "get", lambda *a, **k: (_ for _ in ()).throw(requests.exceptions.Timeout("timeout")))

        with pytest.raises(RuntimeError, match="FAIL HARD: LM Studio connection failed: timeout"):
            self.client.is_server_running()
