import unittest
import os
import pandas as pd
import numpy as np
import unittest
import sys
import os
src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../src'))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)
from backtesting import lib
from unittest.mock import MagicMock, patch

REAL_DATA_PATH = 'tests/RealTestData/dax_200_bars.csv'
REQUIRED_COLS = ['Open', 'High', 'Low', 'Close', 'Volume']

class TestBacktestingLibJaeger(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        if not os.path.exists(REAL_DATA_PATH):
            raise FileNotFoundError('UNBREAKABLE RULE VIOLATION: Real test data missing at ' + REAL_DATA_PATH)
        cls.df = pd.read_csv(REAL_DATA_PATH)
        if 'DateTime' in cls.df.columns:
            cls.df['DateTime'] = pd.to_datetime(cls.df['DateTime'])
            cls.df = cls.df.set_index('DateTime')
        for col in REQUIRED_COLS:
            if col not in cls.df.columns:
                raise AssertionError(f'UNBREAKABLE RULE VIOLATION: Missing required column: {col}')
        if cls.df[['Open','High','Low','Close']].isnull().any().any():
            raise AssertionError('UNBREAKABLE RULE VIOLATION: NaN found in OHLC columns')

    def test_ohlcv_agg_and_trades_agg(self):
        # Test OHLCV_AGG
        ohlcv_agg = lib.OHLCV_AGG
        self.assertEqual(list(ohlcv_agg.keys()), ['Open', 'High', 'Low', 'Close', 'Volume'])
        # Test TRADES_AGG keys
        trades_agg = lib.TRADES_AGG
        self.assertIn('Size', trades_agg)

    def test_barssince(self):
        close_gt_open = self.df['Close'] > self.df['Open']
        result = lib.barssince(close_gt_open)
        self.assertTrue(isinstance(result, (int, float)))
        # If never true, returns np.inf
        always_false = np.zeros(len(self.df), dtype=bool)
        self.assertEqual(lib.barssince(always_false), np.inf)

    def test_cross_and_crossover(self):
        close = self.df['Close'].values
        open_ = self.df['Open'].values
        # Cross: should be True if there is any cross
        result = lib.cross(close, open_)
        self.assertTrue(isinstance(result, (bool, np.bool_)))
        # Crossover: True if close crossed over open
        result = lib.crossover(close, open_)
        self.assertTrue(isinstance(result, (bool, np.bool_)))
        
        # Test IndexError condition with empty arrays
        empty_array = np.array([])
        result = lib.crossover(empty_array, empty_array)
        self.assertFalse(result)
        
        # Test with single element arrays
        single_element = np.array([1.0])
        result = lib.crossover(single_element, single_element)
        self.assertFalse(result)

    def test_quantile(self):
        close = self.df['Close'].values
        # Quantile value
        qval = lib.quantile(close, 0.5)
        self.assertTrue(isinstance(qval, (float, np.floating)))
        # Quantile rank
        qrank = lib.quantile(close)
        self.assertTrue(isinstance(qrank, float))
        
        # Test IndexError condition with empty array
        empty_array = np.array([])
        result = lib.quantile(empty_array)
        self.assertTrue(np.isnan(result))
        
        # Test assertion error for invalid quantile values
        with self.assertRaises(AssertionError) as context:
            lib.quantile(close, -0.1)
        self.assertIn("quantile must be within [0, 1]", str(context.exception))
        
        with self.assertRaises(AssertionError) as context:
            lib.quantile(close, 1.1)
        self.assertIn("quantile must be within [0, 1]", str(context.exception))

    def test_resample_apply(self):
        # Use Close as a Series with datetime index
        close = self.df['Close']
        # Simple moving average
        def sma(series, n):
            return pd.Series(series).rolling(n).mean()
        res = lib.resample_apply('5T', sma, close, 2)
        self.assertIsInstance(res, pd.Series)
        self.assertEqual(res.index[-1], close.index[-1])
        
        # Test with None function (should use identity function)
        res_none = lib.resample_apply('5T', None, close)
        self.assertIsInstance(res_none, pd.Series)
        
        # Test with DataFrame input
        ohlc_df = self.df[['Open', 'High', 'Low', 'Close']]
        res_df = lib.resample_apply('5T', None, ohlc_df)
        self.assertIsInstance(res_df, pd.DataFrame)
        
        # Test assertion error for non-callable function
        with self.assertRaises(AssertionError) as context:
            lib.resample_apply('5T', "not_callable", close)
        self.assertIn("resample_apply(func=) must be callable", str(context.exception))

    def test_plot_heatmaps_wrapper(self):
        # Test that the wrapper function exists and can be called
        # Create a minimal MultiIndex Series to test the wrapper
        import pandas as pd
        index = pd.MultiIndex.from_tuples([(1, 2), (3, 4)], names=['param1', 'param2'])
        heatmap = pd.Series([0.1, 0.2], index=index, name='Return')
        
        # Test that the function can be called (it will delegate to _plotting.py)
        try:
            result = lib.plot_heatmaps(heatmap, filename='test_wrapper.png', open_browser=False)
            # Clean up any generated file
            import os
            if os.path.exists('test_wrapper.png'):
                os.remove('test_wrapper.png')
        except ImportError:
            # Expected if matplotlib/seaborn not available
            pass

    def test_additional_lib_functions(self):
        """Test additional lib functions to improve coverage"""
        import pandas as pd
        
        # Test resample_apply with datetime index (correct parameter order: rule, func, series)
        test_series = pd.Series([1, 2, 3, 4, 5], 
                               index=pd.date_range('2020-01-01', periods=5, freq='D'),
                               name='test')
        # This should work without errors
        result = lib.resample_apply('2D', None, test_series)
        self.assertIsNotNone(result)
    
    def test_barssince_comprehensive(self):
        """Test barssince function with various conditions"""
        # Test with condition that is never True
        never_true = [False, False, False, False]
        result = lib.barssince(never_true)
        self.assertEqual(result, np.inf)
        
        # Test with custom default value
        result = lib.barssince(never_true, default=999)
        self.assertEqual(result, 999)
        
        # Test with condition that was True recently
        recent_true = [False, False, True, False, False]
        result = lib.barssince(recent_true)
        self.assertEqual(result, 2)  # 2 bars since last True
        
        # Test with condition that is True at the end
        end_true = [False, False, False, True]
        result = lib.barssince(end_true)
        self.assertEqual(result, 0)  # 0 bars since last True
    
    @patch('backtesting.lib._compute_stats')
    def test_compute_stats_with_custom_trades(self, mock_compute_stats):
        """Test compute_stats function with custom trades parameter"""
        # Create mock stats object
        mock_stats = MagicMock()
        mock_equity_curve = pd.DataFrame({
            'Equity': [100000, 101000, 102000, 101500, 103000]
        }, index=pd.date_range('2020-01-01', periods=5, freq='D'))
        mock_stats._equity_curve = mock_equity_curve
        mock_stats._trades = pd.DataFrame({
            'Size': [1, -1, 1],
            'EntryBar': [0, 1, 3],
            'ExitBar': [1, 3, 4],
            'PnL': [1000, -500, 1500]
        })
        mock_stats._strategy = MagicMock()
        
        # Create test data
        test_data = self.df.copy()
        
        # Test with custom trades (subset of original trades)
        custom_trades = mock_stats._trades.iloc[:2]  # Only first 2 trades
        mock_compute_stats.return_value = pd.Series({'Return': 0.05})
        
        result = lib.compute_stats(
            stats=mock_stats,
            data=test_data,
            trades=custom_trades,
            risk_free_rate=0.02
        )
        
        # Verify that _compute_stats was called
        mock_compute_stats.assert_called_once()
        call_args = mock_compute_stats.call_args
        
        # Check that trades parameter was passed correctly
        self.assertTrue(call_args[1]['trades'].equals(custom_trades))
        self.assertEqual(call_args[1]['risk_free_rate'], 0.02)
        self.assertTrue(call_args[1]['ohlc_data'].equals(test_data))
        
        # Verify result
        self.assertIsInstance(result, pd.Series)
    
    @patch('backtesting.lib._compute_stats')
    def test_compute_stats_without_custom_trades(self, mock_compute_stats):
        """Test compute_stats function without custom trades parameter"""
        # Create mock stats object
        mock_stats = MagicMock()
        mock_equity_curve = pd.DataFrame({
            'Equity': [100000, 101000, 102000]
        }, index=pd.date_range('2020-01-01', periods=3, freq='D'))
        mock_stats._equity_curve = mock_equity_curve
        mock_stats._trades = pd.DataFrame({
            'Size': [1, -1],
            'EntryBar': [0, 1],
            'ExitBar': [1, 2],
            'PnL': [1000, -500]
        })
        mock_stats._strategy = MagicMock()
        
        # Create test data
        test_data = self.df.copy()
        
        mock_compute_stats.return_value = pd.Series({'Return': 0.03})
        
        result = lib.compute_stats(
            stats=mock_stats,
            data=test_data
        )
        
        # Verify that _compute_stats was called with original trades
        mock_compute_stats.assert_called_once()
        call_args = mock_compute_stats.call_args
        
        # Check that original trades were used
        self.assertTrue(call_args[1]['trades'].equals(mock_stats._trades))
        self.assertEqual(call_args[1]['risk_free_rate'], 0.0)  # Default value
        
        # Verify result
        self.assertIsInstance(result, pd.Series)

if __name__ == '__main__':
    unittest.main()
