import unittest
import os
import importlib.util
import pandas as pd

class TestBacktestingRuleParser(unittest.TestCase):
    def setUp(self):
        self.parser_path = os.path.join(os.path.dirname(__file__), '../src/backtesting_rule_parser.py')
        spec = importlib.util.spec_from_file_location('backtesting_rule_parser', self.parser_path)
        self.parser = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(self.parser)

    def test_module_import(self):
        self.assertTrue(hasattr(self.parser, 'parse_rule') or hasattr(self.parser, 'BacktestingRuleParser'))

    def test_no_hardcoded_params(self):
        with open(self.parser_path, 'r') as f:
            content = f.read()
        self.assertNotIn('="', content, msg='UNBREAKABLE RULE VIOLATION: Hardcoded parameter found in backtesting_rule_parser.py')

    def test_parse_valid_llm_response(self):
        llm_response = (
            "**PATTERN 1: Simple Long Breakout\n"
            "Market Logic: Range expansion\n"
            "Entry Logic: Close > High[-1]\n"
            "Direction: long\n"
            "Stop Logic: Low[-1] - 5\n"
            "Target Logic: Close + 10\n"
            "Position Size: 1.0\n"
            "Timeframe: 5min\n"
        )
        parser = self.parser.BacktestingRuleParser()
        rules = parser.parse_llm_response(llm_response)
        self.assertEqual(len(rules), 1)
        rule = rules[0]
        self.assertEqual(rule.direction, 'long')
        self.assertEqual(rule.timeframe, '5min')
        self.assertEqual(rule.position_size, 1.0)
        self.assertIn('breakout', rule.name.lower())

    def test_parse_missing_required_fields(self):
        # All required fields are whitespace only, which should trigger error
        llm_response = (
            "**PATTERN 1: Incomplete\n"
            "Market Logic: Test\n"
            "Entry Logic:    \n"  # whitespace only
            "Direction:   \n"   # whitespace only
            "Stop Logic:   \n"  # whitespace only
            "Target Logic:   \n"  # whitespace only
            "Position Size: 1.0\n"
        )
        parser = self.parser.BacktestingRuleParser()
        with self.assertRaises(self.parser.BacktestingRuleParseError):
            parser.parse_llm_response(llm_response)

    def test_direction_normalization_and_validation(self):
        llm_response = (
            "Pattern 1: Short Pattern\n"
            "Market Logic: Test\n"
            "Entry Logic: Close < Low[-1]\n"
            "Direction: SHORT\n"
            "Stop Logic: High[-1] + 5\n"
            "Target Logic: Close - 10\n"
            "Position Size: 0.5\n"
        )
        parser = self.parser.BacktestingRuleParser()
        rules = parser.parse_llm_response(llm_response)
        self.assertEqual(rules[0].direction, 'short')
        # Invalid direction
        llm_response_invalid = llm_response.replace('SHORT', 'sideways')
        with self.assertRaises(self.parser.BacktestingRuleParseError):
            parser.parse_llm_response(llm_response_invalid)

    def test_no_fallback_violation(self):
        llm_response = "Some random text with no pattern headers."
        parser = self.parser.BacktestingRuleParser()
        with self.assertRaises(Exception) as ctx:
            parser.parse_llm_response(llm_response)
        self.assertIn('UNBREAKABLE RULE VIOLATION', str(ctx.exception))

    def test_numeric_parsing_and_defaults(self):
        # Position size missing or invalid
        llm_response = (
            "Pattern 1: Default Size\n"
            "Market Logic: Test\n"
            "Entry Logic: Close > Open\n"
            "Direction: long\n"
            "Stop Logic: Low[-1] - 1\n"
            "Target Logic: Close + 2\n"
        )
        parser = self.parser.BacktestingRuleParser()
        rules = parser.parse_llm_response(llm_response)
        self.assertEqual(rules[0].position_size, 1.0)
        # Invalid position size
        llm_response_invalid = llm_response + "Position Size: not_a_number\n"
        rules = parser.parse_llm_response(llm_response_invalid)
        self.assertEqual(rules[0].position_size, 1.0)

    def test_generate_python_functions(self):
        llm_response = (
            "Pattern 1: Valid\n"
            "Market Logic: Test\n"
            "Entry Logic: Close > Open\n"
            "Direction: long\n"
            "Stop Logic: Low[-1] - 1\n"
            "Target Logic: Close + 2\n"
        )
        parser = self.parser.BacktestingRuleParser()
        parser.parse_llm_response(llm_response)
        funcs = parser.generate_python_functions()
        self.assertTrue(callable(funcs[0]))

    def test_calculate_stop_price_methods(self):
        """Test various stop price calculation methods"""
        parser = self.parser.BacktestingRuleParser()
        
        # Create test data
        current = pd.Series({'Low': 100, 'High': 110})
        previous = pd.Series({'Low': 95, 'High': 105})
        entry_price = 108.0
        
        # Test previous_low
        stop = parser._calculate_stop_price('previous_low', entry_price, current, previous, 'long')
        self.assertEqual(stop, 95)
        
        # Test previous_high
        stop = parser._calculate_stop_price('previous_high', entry_price, current, previous, 'short')
        self.assertEqual(stop, 105)
        
        # Test current_low
        stop = parser._calculate_stop_price('current_low', entry_price, current, previous, 'long')
        self.assertEqual(stop, 100)
        
        # Test current_high
        stop = parser._calculate_stop_price('current_high', entry_price, current, previous, 'short')
        self.assertEqual(stop, 110)
        
        # Test percentage-based for long
        stop = parser._calculate_stop_price('2.5%', entry_price, current, previous, 'long')
        self.assertAlmostEqual(stop, entry_price * 0.975, places=2)
        
        # Test percentage-based for short
        stop = parser._calculate_stop_price('3%', entry_price, current, previous, 'short')
        self.assertAlmostEqual(stop, entry_price * 1.03, places=2)
        
        # Test unrecognized logic returns None
        stop = parser._calculate_stop_price('unknown_logic', entry_price, current, previous, 'long')
        self.assertIsNone(stop)

    def test_calculate_target_price_methods(self):
        """Test various target price calculation methods"""
        parser = self.parser.BacktestingRuleParser()
        entry_price = 100.0
        stop_price = 95.0
        
        # Test risk-reward based with default multiplier
        target = parser._calculate_target_price('entry_price + (entry_price - stop_price)', entry_price, stop_price, 'long')
        self.assertEqual(target, 110.0)  # 100 + (5 * 2)
        
        # Test risk-reward based with custom multiplier
        target = parser._calculate_target_price('entry_price + (entry_price - stop_price) * 3', entry_price, stop_price, 'long')
        self.assertEqual(target, 115.0)  # 100 + (5 * 3)
        
        # Test risk-reward for short
        target = parser._calculate_target_price('entry_price - (stop_price - entry_price)', entry_price, 105.0, 'short')
        self.assertEqual(target, 90.0)  # 100 - (5 * 2)
        
        # Test percentage-based for long
        target = parser._calculate_target_price('5%', entry_price, stop_price, 'long')
        self.assertEqual(target, 105.0)
        
        # Test percentage-based for short
        target = parser._calculate_target_price('3%', entry_price, stop_price, 'short')
        self.assertEqual(target, 97.0)
        
        # Test minimum multiplier enforcement (less than 1.5 should become 1.5)
        target = parser._calculate_target_price('entry_price + (entry_price - stop_price) * 1.0', entry_price, stop_price, 'long')
        self.assertEqual(target, 107.5)  # 100 + (5 * 1.5)
        
        # Test unrecognized logic returns None
        target = parser._calculate_target_price('unknown_logic', entry_price, stop_price, 'long')
        self.assertIsNone(target)
        
        # Test with None stop_price
        target = parser._calculate_target_price('entry_price + (entry_price - stop_price)', entry_price, None, 'long')
        self.assertIsNone(target)

    def test_parse_backtesting_rules_function(self):
        """Test the main parse_backtesting_rules function"""
        llm_response = (
            "Pattern 1: Valid\n"
            "Market Logic: Test\n"
            "Entry Logic: Close > Open\n"
            "Direction: long\n"
            "Stop Logic: Low[-1] - 1\n"
            "Target Logic: Close + 2\n"
        )
        
        # Test successful parsing
        functions = self.parser.parse_backtesting_rules(llm_response)
        self.assertTrue(len(functions) > 0)
        self.assertTrue(callable(functions[0]))
        
        # Test failed parsing returns empty list
        invalid_response = "No valid patterns here"
        functions = self.parser.parse_backtesting_rules(invalid_response)
        self.assertEqual(len(functions), 0)

    def test_edge_cases_and_error_handling(self):
        """Test edge cases and error handling scenarios"""
        parser = self.parser.BacktestingRuleParser()
        
        # Test with backticks in logic (should be cleaned)
        llm_response = (
            "Pattern 1: Backticks\n"
            "Market Logic: Test\n"
            "Entry Logic: `Close > Open`\n"
            "Direction: long\n"
            "Stop Logic: `previous_low`\n"
            "Target Logic: `2.5%`\n"
            "Position Size: 1.0\n"
        )
        rules = parser.parse_llm_response(llm_response)
        self.assertEqual(len(rules), 1)
        
        # Test multiple patterns with some failing
        mixed_response = (
            "Pattern 1: Valid\n"
            "Market Logic: Test\n"
            "Entry Logic: Close > Open\n"
            "Direction: long\n"
            "Stop Logic: Low[-1]\n"
            "Target Logic: Close + 5\n\n"
            "Pattern 2: Invalid\n"
            "Market Logic: Test\n"
            "Entry Logic:   \n"  # Empty
            "Direction:   \n"   # Empty
            "Stop Logic:   \n"  # Empty
            "Target Logic:   \n"  # Empty
        )
        
        # Should parse the valid pattern and add error for invalid one
        rules = parser.parse_llm_response(mixed_response)
        self.assertEqual(len(rules), 1)
        self.assertTrue(len(parser.validation_errors) > 0)

    def test_rule_function_execution_with_real_data(self):
        """Test rule function execution using real market data (covers lines 136-163)"""
        # Load real test data - UNBREAKABLE RULE: must use real data
        data_path = os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_200_bars.csv')
        if not os.path.exists(data_path):
            raise FileNotFoundError("UNBREAKABLE RULE VIOLATION: Real test data missing")
        
        data = pd.read_csv(data_path)
        data['DateTime'] = pd.to_datetime(data['DateTime'])
        data.set_index('DateTime', inplace=True)
        
        parser = self.parser.BacktestingRuleParser()
        
        # Test valid long rule execution
        llm_response = (
            "Pattern 1: Long Breakout\n"
            "Market Logic: Test\n"
            "Entry Logic: current_close > previous_high\n"
            "Direction: long\n"
            "Stop Logic: previous_low\n"
            "Target Logic: 3%\n"
            "Position Size: 1.0\n"
        )
        
        rules = parser.parse_llm_response(llm_response)
        functions = parser.generate_python_functions()
        rule_func = functions[0]
        
        # Test edge cases for rule function
        # Test with invalid index (too small)
        result = rule_func(data, 0)
        self.assertIsNone(result)
        
        # Test with invalid index (too large)
        result = rule_func(data, len(data))
        self.assertIsNone(result)
        
        # Test with valid index but no signal
        result = rule_func(data, 10)
        # Result could be None or a valid signal depending on data
        
        # Test short rule with validation
        short_response = (
            "Pattern 1: Short Breakdown\n"
            "Market Logic: Test\n"
            "Entry Logic: current_close < previous_low\n"
            "Direction: short\n"
            "Stop Logic: previous_high\n"
            "Target Logic: 2%\n"
            "Position Size: 0.5\n"
        )
        
        short_rules = parser.parse_llm_response(short_response)
        short_functions = parser.generate_python_functions()
        short_func = short_functions[0]
        
        # Test short rule execution
        result = short_func(data, 50)
        # Result validation happens in the function
        
    def test_entry_condition_evaluation_comprehensive(self):
        """Test comprehensive entry condition evaluation (covers lines 177-209)"""
        parser = self.parser.BacktestingRuleParser()
        
        # Create test data
        current = pd.Series({'Close': 105, 'High': 107, 'Low': 103, 'Open': 104})
        previous = pd.Series({'Close': 100, 'High': 102, 'Low': 98, 'Open': 99})
        
        # Test all specific condition patterns
        self.assertTrue(parser._evaluate_entry_condition('current_close > previous_high', current, previous))
        self.assertFalse(parser._evaluate_entry_condition('current_close < previous_low', current, previous))
        self.assertTrue(parser._evaluate_entry_condition('current_close > previous_close', current, previous))
        self.assertFalse(parser._evaluate_entry_condition('current_close < previous_close', current, previous))
        
        # Test eval-based conditions with various operators
        self.assertTrue(parser._evaluate_entry_condition('current_close > 104', current, previous))
        self.assertFalse(parser._evaluate_entry_condition('current_close < 100', current, previous))
        
        # Test complex eval conditions
        self.assertTrue(parser._evaluate_entry_condition('current_high > previous_high', current, previous))
        self.assertTrue(parser._evaluate_entry_condition('current_low > previous_low', current, previous))
        
        # Test conditions that should return False
        self.assertFalse(parser._evaluate_entry_condition('invalid_condition', current, previous))
        self.assertFalse(parser._evaluate_entry_condition('no_operators_here', current, previous))
        
        # Test eval exception handling
        self.assertFalse(parser._evaluate_entry_condition('current_close > invalid_var', current, previous))
        self.assertFalse(parser._evaluate_entry_condition('malformed > > condition', current, previous))
        
        # Test backtick cleaning
        self.assertTrue(parser._evaluate_entry_condition('`current_close > previous_close`', current, previous))
        
    def test_rule_validation_logic(self):
        """Test rule validation logic for long and short positions"""
        parser = self.parser.BacktestingRuleParser()
        
        # Test long position validation failure (stop >= entry)
        long_response = (
            "Pattern 1: Invalid Long\n"
            "Market Logic: Test\n"
            "Entry Logic: current_close > previous_close\n"
            "Direction: long\n"
            "Stop Logic: current_high\n"  # This will make stop >= entry
            "Target Logic: 5%\n"
            "Position Size: 1.0\n"
        )
        
        rules = parser.parse_llm_response(long_response)
        functions = parser.generate_python_functions()
        
        # Create test data where stop would be >= entry
        data = pd.DataFrame({
            'Close': [100, 105],
            'High': [102, 110],  # Current high > current close
            'Low': [98, 103],
            'Open': [99, 104]
        })
        
        result = functions[0](data, 1)
        self.assertIsNone(result)  # Should fail validation
        
        # Test short position validation failure (stop <= entry)
        short_response = (
            "Pattern 1: Invalid Short\n"
            "Market Logic: Test\n"
            "Entry Logic: current_close < previous_close\n"
            "Direction: short\n"
            "Stop Logic: current_low\n"  # This will make stop <= entry
            "Target Logic: 3%\n"
            "Position Size: 1.0\n"
        )
        
        short_rules = parser.parse_llm_response(short_response)
        short_functions = parser.generate_python_functions()
        
        # Create test data where stop would be <= entry
        data = pd.DataFrame({
            'Close': [105, 100],
            'High': [107, 102],
            'Low': [103, 95],  # Current low < current close
            'Open': [104, 101]
        })
        
        result = short_functions[0](data, 1)
        self.assertIsNone(result)  # Should fail validation
        
    def test_none_price_handling(self):
        """Test handling of None prices in rule functions"""
        parser = self.parser.BacktestingRuleParser()
        
        # Create a rule that will return None for stop or target
        response = (
            "Pattern 1: None Price Test\n"
            "Market Logic: Test\n"
            "Entry Logic: current_close > previous_close\n"
            "Direction: long\n"
            "Stop Logic: unknown_logic\n"  # This will return None
            "Target Logic: 2%\n"
            "Position Size: 1.0\n"
        )
        
        rules = parser.parse_llm_response(response)
        functions = parser.generate_python_functions()
        
        data = pd.DataFrame({
            'Close': [100, 105],
            'High': [102, 107],
            'Low': [98, 103],
            'Open': [99, 104]
        })
        
        result = functions[0](data, 1)
        self.assertIsNone(result)  # Should return None due to None stop price

if __name__ == '__main__':
    unittest.main()
