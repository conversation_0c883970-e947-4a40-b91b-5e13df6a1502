import unittest
import os
import pandas as pd
import numpy as np
import unittest
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend for CI
import sys
src_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../src'))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)
from backtesting._plotting import set_bokeh_output, plot, plot_heatmaps
from backtesting._stats import compute_stats

REAL_DATA_PATH = 'tests/RealTestData/dax_200_bars.csv'
REQUIRED_COLS = ['Open', 'High', 'Low', 'Close', 'Volume']

class TestBacktestingPlottingJaeger(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        if not os.path.exists(REAL_DATA_PATH):
            raise FileNotFoundError('UNBREAKABLE RULE VIOLATION: Real test data missing at ' + REAL_DATA_PATH)
        cls.df = pd.read_csv(REAL_DATA_PATH)
        if 'DateTime' in cls.df.columns:
            cls.df['DateTime'] = pd.to_datetime(cls.df['DateTime'])
            cls.df = cls.df.set_index('DateTime')
        for col in REQUIRED_COLS:
            if col not in cls.df.columns:
                raise AssertionError(f'UNBREAKABLE RULE VIOLATION: Missing required column: {col}')
        if cls.df[['Open','High','Low','Close']].isnull().any().any():
            raise AssertionError('UNBREAKABLE RULE VIOLATION: NaN found in OHLC columns')
        # Minimal stats for plotting
        equity = np.full(len(cls.df), 100000.0)
        cls.stats = compute_stats([], equity, cls.df, strategy_instance=None)

    def test_set_bokeh_output(self):
        # Should not error even if bokeh is not installed
        set_bokeh_output()
        # Simulate ImportError for bokeh to trigger warning
        import sys
        import builtins
        import warnings
        orig_import = builtins.__import__
        def fake_import(name, *args, **kwargs):
            if name.startswith('bokeh'):
                raise ImportError
            return orig_import(name, *args, **kwargs)
        builtins.__import__ = fake_import
        try:
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                set_bokeh_output()
                # Check that warning was issued
                self.assertTrue(len(w) > 0)
                self.assertTrue("Bokeh not available" in str(w[0].message))
        finally:
            builtins.__import__ = orig_import

    def test_plot(self):
        # Should not error and should create a file
        filename = 'test_plot.png'
        html_artifact = filename + '.html'
        if os.path.exists(filename):
            os.remove(filename)
        if os.path.exists(html_artifact):
            os.remove(html_artifact)
        plot(results=self.stats, df=self.df, indicators=[], filename=filename)
        # Accept either .png or .png.html as the output (depending on backend)
        file_created = os.path.exists(filename) or os.path.exists(html_artifact)
        if not file_created:
            import sys
            print(f"DEBUG: CWD={os.getcwd()}, filename={filename}, html_artifact={html_artifact}")
            self.fail(f"Plot file not created: {filename} or {html_artifact}")
        if os.path.exists(filename):
            os.remove(filename)
        if os.path.exists(html_artifact):
            os.remove(html_artifact)
        
        # Test None results case
        result = plot(results=None, df=self.df, indicators=[])
        self.assertIsNone(result)
        
        # Test None df case
        result = plot(results=self.stats, df=None, indicators=[])
        self.assertIsNone(result)
        
        # Test both None case
        result = plot(results=None, df=None, indicators=[])
        self.assertIsNone(result)
        
        # Test without filename (open_browser=False to avoid showing plot)
        result = plot(results=self.stats, df=self.df, indicators=[], open_browser=False)
        self.assertIsNotNone(result)
        
        # Simulate ImportError for matplotlib
        import sys
        import builtins
        orig_import = builtins.__import__
        def fake_import(name, *args, **kwargs):
            if name.startswith('matplotlib'):
                raise ImportError
            return orig_import(name, *args, **kwargs)
        builtins.__import__ = fake_import
        try:
            result = plot(results=self.stats, df=self.df, indicators=[])
            self.assertIsNone(result)
        finally:
            builtins.__import__ = orig_import

    def test_plot_heatmaps(self):
        # Test with empty heatmap
        empty_heatmap = pd.Series([], dtype=float)
        plot_heatmaps(empty_heatmap, 'max', 3, filename='', open_browser=False)
        
        # Test with MultiIndex Series (proper heatmap data)
        index = pd.MultiIndex.from_product([[1, 2], [10, 20]], names=['param1', 'param2'])
        heatmap_series = pd.Series([0.5, 0.6, 0.7, 0.8], index=index, name='Return')
        
        filename = 'test_heatmap.png'
        if os.path.exists(filename):
            os.remove(filename)
            
        plot_heatmaps(heatmap_series, 'max', 3, filename=filename, open_browser=False)
        
        # Check if file was created
        if os.path.exists(filename):
            os.remove(filename)
        
        # Test without filename
        plot_heatmaps(heatmap_series, 'max', 3, filename='', open_browser=False)
        
        # Test with MultiIndex but insufficient levels
        single_index = pd.MultiIndex.from_product([[1]], names=['param1'])
        single_heatmap = pd.Series([0.5], index=single_index, name='Return')
        plot_heatmaps(single_heatmap, 'max', 3, filename='', open_browser=False)
        
        # Test with non-MultiIndex Series
        simple_series = pd.Series([0.1, 0.2, 0.3], name='Return')
        plot_heatmaps(simple_series, 'max', 3, filename='', open_browser=False)
        
        # Test ImportError for matplotlib/seaborn
        import builtins
        orig_import = builtins.__import__
        def fake_import(name, *args, **kwargs):
            if name.startswith('matplotlib') or name.startswith('seaborn'):
                raise ImportError
            return orig_import(name, *args, **kwargs)
        builtins.__import__ = fake_import
        try:
            result = plot_heatmaps(heatmap_series, 'max', 3, filename='', open_browser=False)
            self.assertIsNone(result)
        finally:
            builtins.__import__ = orig_import
    
    def test_plot_with_equity_curve(self):
        """Test plot function with equity curve data"""
        # Create a mock results object with _equity_curve
        class MockResults:
            def __init__(self, df_index):
                equity_data = pd.DataFrame({
                    'Equity': [100000, 101000, 102000, 101500, 103000]
                }, index=df_index[:5])
                self._equity_curve = equity_data
        
        mock_results = MockResults(self.df.index)
        
        # Test with equity curve plotting enabled
        result = plot(results=mock_results, df=self.df, plot_equity=True, open_browser=False)
        self.assertIsNotNone(result)
        
        # Test with equity curve plotting disabled
        result = plot(results=mock_results, df=self.df, plot_equity=False, open_browser=False)
        self.assertIsNotNone(result)
    
    def test_plot_heatmaps_with_actual_plotting(self):
        """Test plot_heatmaps with actual matplotlib/seaborn plotting"""
        # Test with MultiIndex Series that should trigger actual plotting
        index = pd.MultiIndex.from_product([[1, 2, 3], [10, 20, 30]], names=['param1', 'param2'])
        heatmap_series = pd.Series([0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3], index=index, name='Return')
        
        # Test with filename to trigger file saving
        filename = 'test_heatmap_actual.png'
        if os.path.exists(filename):
            os.remove(filename)
            
        # This should execute the actual plotting code
        plot_heatmaps(heatmap_series, 'max', 3, filename=filename, open_browser=False)
        
        # Clean up if file was created
        if os.path.exists(filename):
            os.remove(filename)
        
        # Test with open_browser=True (but matplotlib backend should prevent actual display)
        plot_heatmaps(heatmap_series, 'max', 3, filename='', open_browser=True)
        
        # Test the actual seaborn plotting path with proper MultiIndex
        # This should trigger the sns.heatmap call and plt.title call
        try:
            import seaborn as sns
            import matplotlib.pyplot as plt
            
            # Create a proper MultiIndex with 2+ levels to trigger pivot logic
            multi_index = pd.MultiIndex.from_product(
                [[1, 2], [10, 20]], 
                names=['strategy_param', 'risk_param']
            )
            test_series = pd.Series([0.15, 0.25, 0.35, 0.45], index=multi_index, name='sharpe_ratio')
            
            # This should execute the full seaborn plotting path
            plot_heatmaps(test_series, agg='max', ncols=3, filename='', open_browser=False)
            
        except ImportError:
            # If seaborn not available, skip this part
            pass
    
    def test_plot_edge_cases(self):
        """Test additional edge cases for plot function"""
        # Test with DataFrame missing Close column
        df_no_close = self.df.drop('Close', axis=1)
        result = plot(results=self.stats, df=df_no_close, open_browser=False)
        self.assertIsNotNone(result)
        
        # Test with results that don't have _equity_curve
        class MockResultsNoEquity:
            pass
        
        mock_results_no_equity = MockResultsNoEquity()
        result = plot(results=mock_results_no_equity, df=self.df, plot_equity=True, open_browser=False)
        self.assertIsNotNone(result)
        
        # Test file creation assertion error path by mocking os.path.exists
        class MockResults:
            def __init__(self, df_index):
                equity_data = pd.DataFrame({
                    'Equity': [100000, 101000, 102000, 101500, 103000]
                }, index=df_index[:5])
                self._equity_curve = equity_data
        
        mock_results = MockResults(self.df.index)
        
        # Mock os.path.exists to return False to trigger assertion error
        import unittest.mock
        with unittest.mock.patch('os.path.exists', return_value=False):
            try:
                plot(results=mock_results, df=self.df, filename='test.png', open_browser=False)
                self.fail("Expected AssertionError for file not created")
            except AssertionError as e:
                self.assertIn("UNBREAKABLE RULE VIOLATION", str(e))
    
    def test_plot_heatmaps_edge_cases(self):
        """Test additional edge cases for plot_heatmaps function"""
        # Test with MultiIndex but only one level
        single_level_index = pd.Index([1, 2, 3], name='param1')
        single_level_series = pd.Series([0.5, 0.6, 0.7], index=single_level_index, name='Return')
        result = plot_heatmaps(single_level_series, 'max', 3, filename='', open_browser=False)
        
        # Test with MultiIndex having exactly 2 levels (should trigger pivot logic)
        two_level_index = pd.MultiIndex.from_product([[1, 2], [10, 20]], names=['param1', 'param2'])
        two_level_series = pd.Series([0.5, 0.6, 0.7, 0.8], index=two_level_index, name='Return')
        
        # Test with filename to trigger file saving path
        filename = 'test_heatmap_edge.png'
        if os.path.exists(filename):
            os.remove(filename)
        
        result = plot_heatmaps(two_level_series, 'max', 3, filename=filename, open_browser=True)
        
        # Clean up if file was created
        if os.path.exists(filename):
            os.remove(filename)
        
        # Test with MultiIndex series without name
        unnamed_series = pd.Series([0.5, 0.6, 0.7, 0.8], index=two_level_index)
        result = plot_heatmaps(unnamed_series, 'max', 3, filename='', open_browser=False)

if __name__ == '__main__':
    unittest.main()
