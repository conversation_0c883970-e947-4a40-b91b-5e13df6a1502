import os
import pytest
from ai_integration.legacy_situational_prompts import SituationalAnalysisPrompts

REAL_DATA_PATH = os.path.join(os.path.dirname(__file__), 'RealTestData')

def _require_real_data():
    if not os.path.exists(REAL_DATA_PATH):
        raise FileNotFoundError('UNBREAKABLE RULE VIOLATION: Real market data required for all tests.')

def test_fail_on_missing_real_data(monkeypatch):
    monkeypatch.setattr(os.path, "exists", lambda path: False)
    with pytest.raises(FileNotFoundError, match="UNBREAKABLE RULE VIOLATION"):
        _require_real_data()

def test_get_core_situational_questions():
    _require_real_data()
    questions = SituationalAnalysisPrompts.get_core_situational_questions()
    assert isinstance(questions, list)
    assert all(isinstance(q, str) for q in questions)
    assert any("participant" in q.lower() for q in questions)

def test_get_tom_hougaard_examples():
    _require_real_data()
    examples = SituationalAnalysisPrompts.get_tom_hougaard_examples()
    assert isinstance(examples, list)
    assert all(isinstance(e, str) for e in examples)
    assert any("monday" in e.lower() for e in examples)

def test_get_situational_categories():
    _require_real_data()
    cats = SituationalAnalysisPrompts.get_situational_categories()
    assert isinstance(cats, dict)
    assert "temporal_situations" in cats
    assert "participant_behavioral" in cats
    assert isinstance(cats["temporal_situations"], dict)
    assert isinstance(cats["participant_behavioral"], dict)

def test_generate_situational_discovery_prompt_and_analyze_market_regime():
    _require_real_data()
    import pandas as pd
    # STRONG UPTREND
    data = {'Close': pd.Series([100, 104, 110, 120, 130]), 'High': pd.Series([101, 105, 111, 121, 131]), 'Low': pd.Series([99, 103, 109, 119, 129]), 'Open': pd.Series([100, 104, 110, 120, 130])}
    ohlc_data = pd.DataFrame(data)
    ohlc_data['datetime'] = pd.date_range(start='2023-01-01', periods=len(ohlc_data), freq='D')
    profit_context = 'Profit context here.'
    market_summaries = 'Market summaries here.'
    performance_feedback = 'Performance feedback here.'
    prompt = SituationalAnalysisPrompts.generate_situational_discovery_prompt(ohlc_data, profit_context, market_summaries, performance_feedback)
    assert isinstance(prompt, str)
    assert 'MT4-READY' in prompt
    regime = SituationalAnalysisPrompts._analyze_market_regime(ohlc_data)
    assert 'STRONG UPTREND' in regime
    # UPTRENDING
    data = {'Close': pd.Series([100, 101, 101, 102, 102]), 'High': pd.Series([101, 102, 102, 103, 103]), 'Low': pd.Series([99, 100, 100, 101, 101]), 'Open': pd.Series([100, 101, 101, 102, 102])}
    ohlc_data = pd.DataFrame(data)
    regime = SituationalAnalysisPrompts._analyze_market_regime(ohlc_data)
    assert 'UPTRENDING' in regime
    # STRONG DOWNTREND
    data = {'Close': pd.Series([100, 95, 90, 85, 80]), 'High': pd.Series([101, 96, 91, 86, 81]), 'Low': pd.Series([99, 94, 89, 84, 79]), 'Open': pd.Series([100, 95, 90, 85, 80])}
    ohlc_data = pd.DataFrame(data)
    regime = SituationalAnalysisPrompts._analyze_market_regime(ohlc_data)
    assert 'STRONG DOWNTREND' in regime
    # DOWNTRENDING
    data = {'Close': pd.Series([100, 99, 99, 98, 98]), 'High': pd.Series([101, 100, 100, 99, 99]), 'Low': pd.Series([99, 98, 98, 97, 97]), 'Open': pd.Series([100, 99, 99, 98, 98])}
    ohlc_data = pd.DataFrame(data)
    regime = SituationalAnalysisPrompts._analyze_market_regime(ohlc_data)
    assert 'DOWNTRENDING' in regime
    # RANGING
    data = {'Close': pd.Series([100, 100.2, 99.8, 100, 100.1]), 'High': pd.Series([101, 101.2, 100.8, 101, 101.1]), 'Low': pd.Series([99, 99.2, 98.8, 99, 99.1]), 'Open': pd.Series([100, 100.2, 99.8, 100, 100.1])}
    ohlc_data = pd.DataFrame(data)
    regime = SituationalAnalysisPrompts._analyze_market_regime(ohlc_data)
    assert 'RANGING' in regime
    # Exception branch
    class Bad:
        def get(self, *a, **k): raise Exception('fail')
    regime = SituationalAnalysisPrompts._analyze_market_regime(Bad())
    assert 'Market regime analysis unavailable' in regime

def test_generate_situational_validation_prompt():
    _require_real_data()
    import pandas as pd
    discovered_patterns = 'Pattern 1: ...\nPattern 2: ...'
    market_data = pd.DataFrame({
        'datetime': pd.date_range(start='2023-01-01', periods=5, freq='D')
    })
    prompt = SituationalAnalysisPrompts.generate_situational_validation_prompt(discovered_patterns, market_data)
    assert isinstance(prompt, str)
    assert 'VALIDATION CRITERIA' in prompt
