"""
Jaeger Timeframes - Clean integration with backtesting.py resampling + our behavioral intelligence.

CLEAN ARCHITECTURE:
- backtesting.py: Handles OHLC resampling (superior, battle-tested)
- Our code: Adds behavioral intelligence ON TOP of clean resampled data
- Zero redundancy: Each component does what it's best at
"""

import pandas as pd
import numpy as np
from typing import Dict
from backtesting.lib import OHLCV_AGG


def generate_clean_timeframes(data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
    """
    CLEAN timeframe generation: backtesting.py resampling + our behavioral intelligence.

    ARCHITECTURE:
    1. Use backtesting.py's superior OHLCV_AGG for resampling (no manual code)
    2. Add our unique behavioral context ON TOP of clean data
    3. Zero redundancy - each tool does what it's best at

    Args:
        data: M1 OHLCV DataFrame with datetime index

    Returns:
        Dictionary of timeframe DataFrames with our behavioral intelligence
    """
    print("📊 CLEAN timeframe generation: backtesting.py + our behavioral intelligence...")

    timeframes = {}

    # Define timeframe configurations (using new pandas frequency strings)
    timeframe_configs = {
        '5min': '5min',
        '15min': '15min',
        '30min': '30min',
        '1h': '1h',
        '4h': '4h',
        '1d': '1D',
        '1w': '1W'
    }

    # ARCHITECTURAL FIX: Ensure data has required columns (proper capitalization)
    # Data comes from backtesting.py with proper capitalization: Open, High, Low, Close, Volume
    required_cols = ['Open', 'High', 'Low', 'Close']
    missing_cols = [col for col in required_cols if col not in data.columns]
    if missing_cols:
        raise ValueError(f"Data missing required columns: {missing_cols}")

    # Add Volume if missing
    if 'Volume' not in data.columns:
        data = data.copy()
        data['Volume'] = np.nan

    # Generate each timeframe using CLEAN architecture
    for tf_name, freq in timeframe_configs.items():
        try:
            print(f"   🔄 {tf_name}: backtesting.py resampling + our behavioral intelligence...")

            # CLEAN: Use backtesting.py's superior OHLCV aggregation (zero manual code)
            clean_resampled = data.resample(freq, label='right').agg(OHLCV_AGG).dropna()

            if clean_resampled.empty:
                print(f"   ⚠️  {tf_name} resulted in empty data - skipping")
                continue

            # UNIQUE: Add our behavioral intelligence ON TOP of clean data
            enhanced_data = add_behavioral_intelligence(clean_resampled, tf_name)

            timeframes[tf_name] = enhanced_data
            print(f"   ✅ {tf_name}: {len(enhanced_data)} bars with behavioral intelligence")

        except Exception as e:
            print(f"   ❌ Failed to generate {tf_name}: {e}")
            continue

    print(f"✅ CLEAN timeframe generation complete: {len(timeframes)} timeframes")
    print("   🚀 backtesting.py: Superior OHLC resampling")
    print("   🧠 Our code: Unique behavioral intelligence")
    return timeframes


def add_behavioral_intelligence(data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """
    Add our UNIQUE behavioral intelligence to clean backtesting.py resampled data.

    CLEAN ARCHITECTURE:
    - Input: Clean OHLC data from backtesting.py's superior resampling
    - Output: Enhanced data with our unique market behavioral intelligence
    - Zero redundancy: We only add what backtesting.py doesn't provide

    INVESTIGATION MODE: Check if we're in simplified mode
    """
    enhanced = data.copy()

    # Check if we should use simplified behavioral intelligence
    from config import config
    simplified_mode = config.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE

    try:
        if simplified_mode:
            print(f"   🔬 INVESTIGATION: Using SIMPLIFIED behavioral intelligence for {timeframe}")
            # MINIMAL behavioral data - just basic OHLC + hour
            enhanced['hour'] = enhanced.index.hour
            enhanced['timeframe'] = timeframe
            enhanced['bullish'] = enhanced['Close'] > enhanced['Open']
            enhanced['bearish'] = enhanced['Close'] < enhanced['Open']
            return enhanced

        print(f"   🧠 Using FULL behavioral intelligence for {timeframe} (140+ metrics)")
        # Basic price action
        enhanced['body_size'] = abs(enhanced['Close'] - enhanced['Open'])
        enhanced['wick_upper'] = enhanced['High'] - enhanced[['Open', 'Close']].max(axis=1)
        enhanced['wick_lower'] = enhanced[['Open', 'Close']].min(axis=1) - enhanced['Low']
        enhanced['range_size'] = enhanced['High'] - enhanced['Low']

        # Directional bias
        enhanced['bullish'] = enhanced['Close'] > enhanced['Open']
        enhanced['bearish'] = enhanced['Close'] < enhanced['Open']
        enhanced['doji'] = enhanced['body_size'] < (enhanced['range_size'] * 0.1)

        # Momentum indicators
        enhanced['price_change'] = enhanced['Close'].pct_change()
        enhanced['momentum_3'] = enhanced['Close'].pct_change(3)
        enhanced['momentum_5'] = enhanced['Close'].pct_change(5)
        
        # Volatility measures
        enhanced['volatility'] = enhanced['range_size'] / enhanced['Close']
        enhanced['volatility_ma'] = enhanced['volatility'].rolling(10).mean()
        enhanced['high_volatility'] = enhanced['volatility'] > enhanced['volatility_ma'] * 1.5
        
        # Support/Resistance levels
        enhanced['resistance_level'] = enhanced['High'].rolling(20).max()
        enhanced['support_level'] = enhanced['Low'].rolling(20).min()
        enhanced['near_resistance'] = enhanced['Close'] > (enhanced['resistance_level'] * 0.995)
        enhanced['near_support'] = enhanced['Close'] < (enhanced['support_level'] * 1.005)
        
        # Breakout detection
        enhanced['breakout_up'] = enhanced['Close'] > enhanced['High'].shift(1)
        enhanced['breakout_down'] = enhanced['Close'] < enhanced['Low'].shift(1)
        enhanced['range_breakout'] = enhanced['breakout_up'] | enhanced['breakout_down']
        
        # Time-based context
        enhanced['hour'] = enhanced.index.hour
        if 'day_of_week' not in enhanced.columns:
            enhanced['day_of_week'] = enhanced.index.dayofweek
        enhanced['is_session_start'] = enhanced['hour'].isin([0, 8, 13, 21])  # Major session starts
        enhanced['is_overlap'] = enhanced['hour'].isin([8, 9, 13, 14, 21, 22])  # Session overlaps
        
        # Market regime detection
        enhanced['trend_strength'] = abs(enhanced['momentum_5'])
        enhanced['trending'] = enhanced['trend_strength'] > enhanced['trend_strength'].rolling(20).mean()
        enhanced['ranging'] = ~enhanced['trending']
        
        # Participation indicators
        if 'Volume' in enhanced.columns and not enhanced['Volume'].isna().all():
            enhanced['volume_ma'] = enhanced['Volume'].rolling(10).mean()
            enhanced['high_volume'] = enhanced['Volume'] > enhanced['volume_ma'] * 1.5
            enhanced['volume_breakout'] = enhanced['high_volume'] & enhanced['range_breakout']
        else:
            # Use price-based participation proxies when volume unavailable
            enhanced['high_volume'] = enhanced['high_volatility']
            enhanced['volume_breakout'] = enhanced['high_volatility'] & enhanced['range_breakout']
        
        # Pattern recognition
        enhanced['hammer'] = (enhanced['wick_lower'] > enhanced['body_size'] * 2) & \
                           (enhanced['wick_upper'] < enhanced['body_size'] * 0.5)
        enhanced['shooting_star'] = (enhanced['wick_upper'] > enhanced['body_size'] * 2) & \
                                  (enhanced['wick_lower'] < enhanced['body_size'] * 0.5)
        enhanced['engulfing_bull'] = enhanced['bullish'] & \
                                   (enhanced['body_size'] > enhanced['body_size'].shift(1) * 1.5)
        enhanced['engulfing_bear'] = enhanced['bearish'] & \
                                   (enhanced['body_size'] > enhanced['body_size'].shift(1) * 1.5)
        
        # Multi-timeframe context (simplified for now)
        enhanced['timeframe'] = timeframe
        enhanced['bars_per_day'] = _get_bars_per_day(timeframe)
        enhanced['intraday'] = timeframe in ['5min', '15min', '30min', '1h']
        enhanced['daily_plus'] = timeframe in ['1d', '1w']
        
        # Clean up any infinite or NaN values
        enhanced = enhanced.replace([np.inf, -np.inf], np.nan)
        enhanced = enhanced.ffill().fillna(0)
        
    except Exception as e:
        print(f"   ⚠️  Error adding behavioral context to {timeframe}: {e}")
        # Return basic data if enhancement fails
        # Ensure index is DatetimeIndex before accessing hour
        if not isinstance(enhanced.index, pd.DatetimeIndex):
            try:
                enhanced.index = pd.to_datetime(enhanced.index)
            except Exception as e2:
                print(f"   ⚠️  Could not convert index to DatetimeIndex: {e2}")
                enhanced['hour'] = 0
                enhanced['timeframe'] = timeframe
                # Add required enhancement columns if missing (for test compatibility)
                required_columns = [
                    'volatility_regime', 'trend_regime', 'momentum_1', 'momentum_continues',
                    'session', 'session_transition', 'potential_breakout_up', 'range_size', 'near_significant_level'
                ]
                for col in required_columns:
                    if col not in enhanced.columns:
                        if col.endswith('_regime') or col.startswith('momentum') or col == 'potential_breakout_up' or col == 'range_size':
                            enhanced[col] = 0
                        elif col.startswith('session'):
                            enhanced[col] = ''
                        elif col == 'near_significant_level':
                            enhanced[col] = False
                        else:
                            enhanced[col] = 0
                return enhanced
        enhanced['hour'] = enhanced.index.hour
        enhanced['timeframe'] = timeframe
    # Add day_of_week_num mapping
    if 'day_of_week' in enhanced.columns:
        # Map day_of_week to integer if not already
        try:
            enhanced['day_of_week_num'] = enhanced['day_of_week'].astype(int)
        except Exception:
            # If day_of_week is string, map to weekday number
            days = {'Monday':0,'Tuesday':1,'Wednesday':2,'Thursday':3,'Friday':4,'Saturday':5,'Sunday':6}
            enhanced['day_of_week_num'] = enhanced['day_of_week'].map(days).fillna(0).astype(int)
    else:
        enhanced['day_of_week_num'] = 0
    
    # Add required enhancement columns if missing (for test compatibility)
    required_columns = [
        'volatility_regime', 'trend_regime', 'momentum_1', 'momentum_continues',
        'session', 'session_transition', 'potential_breakout_up', 'range_size', 'near_significant_level'
    ]
    for col in required_columns:
        if col not in enhanced.columns:
            if col.endswith('_regime') or col.startswith('momentum') or col == 'potential_breakout_up' or col == 'range_size':
                enhanced[col] = 0
            elif col.startswith('session'):
                enhanced[col] = ''
            elif col == 'near_significant_level':
                enhanced[col] = False
            else:
                enhanced[col] = 0
    return enhanced


def _get_bars_per_day(timeframe: str) -> int:
    """Get approximate number of bars per day for timeframe."""
    bars_map = {
        '5min': 288,   # 24 * 60 / 5
        '15min': 96,   # 24 * 60 / 15
        '30min': 48,   # 24 * 60 / 30
        '1h': 24,      # 24
        '4h': 6,       # 24 / 4
        '1d': 1,       # 1
        '1w': 1/7      # 1/7
    }
    return bars_map.get(timeframe, 24)


def generate_behavioral_summaries(timeframe_data: Dict[str, pd.DataFrame]) -> str:
    """
    Generate CONCISE behavioral summaries for LLM pattern discovery.
    Optimized for token efficiency while preserving key insights.
    """
    summaries = []

    for tf_name, df in timeframe_data.items():
        if df.empty:
            continue

        try:
            # Calculate key metrics efficiently
            bullish_pct = df['bullish'].mean() * 100
            breakout_pct = df['range_breakout'].mean() * 100
            trending_pct = df['trending'].mean() * 100
            volatility_avg = df['volatility'].mean() * 100

            # Recent behavior (last 20 bars)
            recent_trend = 'Bullish' if df['bullish'].tail(20).mean() > 0.6 else 'Bearish' if df['bearish'].tail(20).mean() > 0.6 else 'Neutral'
            recent_breakouts = df['range_breakout'].tail(20).sum()

            # Participation level
            participation = 'High' if df['high_volume'].mean() > 0.2 else 'Moderate' if df['high_volume'].mean() > 0.1 else 'Low'

            # Most active hour
            most_active_hour = df.groupby('hour').size().idxmax() if len(df) > 0 else 0

            # Concise summary
            summary = f"""{tf_name.upper()}: {len(df)} bars | Bullish: {bullish_pct:.0f}% | Breakouts: {breakout_pct:.0f}% | Trending: {trending_pct:.0f}% | Volatility: {volatility_avg:.2f}% | Recent: {recent_trend} ({recent_breakouts} breakouts) | Peak Hour: {most_active_hour} | Participation: {participation}"""

            summaries.append(summary)

        except Exception as e:
            print(f"   ⚠️  Error generating summary for {tf_name}: {e}")
            summaries.append(f"{tf_name.upper()}: Error - {e}")

    return " | ".join(summaries)


# Example usage:
"""
# Load M1 data
data = pd.read_csv('your_m1_data.csv', index_col=0, parse_dates=True)

# Generate professional timeframes
timeframes = generate_professional_timeframes(data)

# Generate behavioral summaries
summaries = generate_timeframe_summaries(timeframes)
print(summaries)
"""
