"""
Data Ingestion Module for Jaeger Trading System

This module handles CSV data loading and preprocessing using backtesting.py's
native data handling capabilities, following the documented architecture where
backtesting.py handles data ingestion and Cortex only coordinates LLM analysis.

Architecture:
CSV Files → backtesting.py (data ingestion & validation) → behavioral_intelligence.py (behavioral intelligence) → Cortex (LLM analysis only)
"""

import pandas as pd
from pathlib import Path
from typing import Dict
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataIngestionManager:
    """
    Manages data ingestion using backtesting.py's native capabilities.
    
    This class replaces the data loading functionality that was incorrectly
    implemented in Cortex, following the documented architecture.
    """
    
    def __init__(self):
        self.supported_formats = ['.csv', '.xlsx', '.xls']
        self.required_columns = ['DateTime', 'Open', 'High', 'Low', 'Close', 'Volume']
    
    def load_market_data(self, data_file: str) -> pd.DataFrame:
        """
        Load market data from file using backtesting.py compatible format.
        
        Args:
            data_file: Path to the market data file
            
        Returns:
            DataFrame with proper datetime index and OHLCV columns or None if insufficient data
            
        Raises:
            ValueError: If file format is unsupported or data is invalid
            FileNotFoundError: If data file doesn't exist
            Exception: For any other data loading errors
        """
        try:
            data = self._load_data_file(data_file)
            if data is None:
                logger.error(f"Failed to load data from {data_file}")
                return None
            
            if len(data) < 50:
                logger.warning(f"Insufficient data in {data_file}: {len(data)} rows (minimum 50 required)")
                return None
            
            # Check if data is completely empty or has no valid columns
            if data.empty or len(data.columns) == 0:
                logger.error(f"Data file {data_file} is empty or has no columns")
                return None
            
            data = self._validate_and_prepare_data(data)
            logger.info(f"Successfully loaded {len(data)} rows of market data from {data_file}")
            return data
            
        except (FileNotFoundError, ValueError) as e:
            # Re-raise expected exceptions
            raise e
        except Exception as e:
            logger.error(f"Unexpected error loading market data from {data_file}: {str(e)}")
            raise Exception(f"Failed to load market data: {str(e)}") from e
    
    def _load_data_file(self, data_file: str) -> pd.DataFrame:
        data_path = Path(data_file)
        
        if not data_path.exists():
            raise FileNotFoundError(f"Data file not found: {data_file}")
        
        if data_path.suffix.lower() not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {data_path.suffix}. Supported formats: {self.supported_formats}")
        
        # Check file size
        file_size = data_path.stat().st_size
        if file_size == 0:
            raise ValueError(f"Data file is empty: {data_file}")
        
        if file_size > 100 * 1024 * 1024:  # 100MB limit
            logger.warning(f"Large file detected ({file_size / (1024*1024):.1f}MB): {data_file}")
        
        logger.info(f"Loading market data from: {data_file}")
        
        try:
            # Load data based on file type
            if data_path.suffix.lower() == '.csv':
                # Try different encodings and separators for CSV files
                try:
                    data = pd.read_csv(data_file, encoding='utf-8')
                except UnicodeDecodeError:
                    logger.warning(f"UTF-8 encoding failed for {data_file}, trying latin-1")
                    data = pd.read_csv(data_file, encoding='latin-1')
            else:
                data = pd.read_excel(data_file)
            
            if data.empty:
                raise ValueError(f"Loaded data is empty from file: {data_file}")
            
            return data
            
        except pd.errors.EmptyDataError:
            raise ValueError(f"No data found in file: {data_file}")
        except pd.errors.ParserError as e:
            raise ValueError(f"Failed to parse file {data_file}: {str(e)}")
        except Exception as e:
            raise ValueError(f"Error reading file {data_file}: {str(e)}")
    
    def _validate_and_prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Validate and prepare data for backtesting.py compatibility.
        
        Args:
            data: Raw DataFrame from file
            
        Returns:
            Validated DataFrame with proper datetime index
        """
        # Check required columns, but attempt to create them if possible
        missing_cols = [col for col in self.required_columns if col not in data.columns]
        # Try to synthesize DateTime from Date and Time
        if 'DateTime' not in data.columns:
            if 'Date' in data.columns and 'Time' in data.columns:
                data['DateTime'] = data['Date'].astype(str) + ' ' + data['Time'].astype(str)
            elif 'datetime' in data.columns:
                data['DateTime'] = data['datetime']
            else:
                raise ValueError("Missing required column: DateTime")
        # For bid/ask format, synthesize OHLC from Bid if needed
        ohlc_missing = any(col not in data.columns for col in ['Open','High','Low','Close'])
        if ohlc_missing and 'Bid' in data.columns:
            data['Open'] = data['Bid']
            data['High'] = data['Bid']
            data['Low'] = data['Bid']
            data['Close'] = data['Bid']
        # After synthesis, check again
        missing_cols = [col for col in self.required_columns if col not in data.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        # Set DateTime as index
        data['DateTime'] = pd.to_datetime(data['DateTime'])
        data.set_index('DateTime', inplace=True)
        
        # Create a copy to avoid modifying original
        prepared_data = data.copy()
        
        # Validate OHLC data integrity
        self._validate_ohlc_integrity(prepared_data)
        
        # Sort by datetime to ensure proper order
        prepared_data.sort_index(inplace=True)
        
        # Remove any duplicate timestamps
        if prepared_data.index.duplicated().any():
            logger.warning("Removing duplicate timestamps")
            prepared_data = prepared_data[~prepared_data.index.duplicated(keep='first')]
        
        return prepared_data
    
    def _validate_ohlc_integrity(self, data: pd.DataFrame) -> None:
        """
        Validate OHLC data integrity according to market data rules.
        
        Args:
            data: DataFrame with OHLC columns
            
        Raises:
            ValueError: If OHLC data violates market data rules
        """
        # Check for negative prices
        price_cols = ['Open', 'High', 'Low', 'Close']
        if (data[price_cols] <= 0).any().any():
            negative_rows = (data[price_cols] <= 0).any(axis=1).sum()
            raise ValueError(f"Found {negative_rows} rows with negative or zero prices in OHLC data")
        
        # Check for infinite values
        if data[price_cols].isin([float('inf'), float('-inf')]).any().any():
            inf_rows = data[price_cols].isin([float('inf'), float('-inf')]).any(axis=1).sum()
            raise ValueError(f"Found {inf_rows} rows with infinite values in OHLC data")
        
        # Check for extremely large price movements (potential data errors)
        for col in price_cols:
            price_changes = data[col].pct_change().abs()
            extreme_changes = price_changes > 0.5  # 50% price change threshold
            if extreme_changes.any():
                extreme_count = extreme_changes.sum()
                logger.warning(f"Found {extreme_count} extreme price changes (>50%) in {col} column")
        
        # Check High >= max(Open, Close) and Low <= min(Open, Close)
        invalid_high = data['High'] < data[['Open', 'Close']].max(axis=1)
        invalid_low = data['Low'] > data[['Open', 'Close']].min(axis=1)
        
        if invalid_high.any():
            invalid_count = invalid_high.sum()
            logger.warning(f"Found {invalid_count} rows with invalid High prices (High < max(Open, Close))")
            # Fix invalid highs
            data.loc[invalid_high, 'High'] = data.loc[invalid_high, ['Open', 'Close']].max(axis=1)
        
        if invalid_low.any():
            invalid_count = invalid_low.sum()
            logger.warning(f"Found {invalid_count} rows with invalid Low prices (Low > min(Open, Close))")
            # Fix invalid lows
            data.loc[invalid_low, 'Low'] = data.loc[invalid_low, ['Open', 'Close']].min(axis=1)
        
        # Check for missing values
        if data.isnull().any().any():
            null_counts = data.isnull().sum()
            total_nulls = null_counts.sum()
            # Only reject if more than 50% missing data (was too strict at 10%)
            if total_nulls > len(data) * 0.5:
                raise ValueError(f"Too many missing values ({total_nulls}/{len(data)} rows): {null_counts[null_counts > 0].to_dict()}")
            logger.warning(f"Found missing values: {null_counts[null_counts > 0].to_dict()}")
            # Forward fill missing values
            data.ffill(inplace=True)
            # If still missing values at the beginning, backward fill
            if data.isnull().any().any():
                data.bfill(inplace=True)
        
        # Check Volume column specifically
        if 'Volume' in data.columns:
            if (data['Volume'] < 0).any():
                negative_volume_count = (data['Volume'] < 0).sum()
                raise ValueError(f"Found {negative_volume_count} rows with negative volume")
            
            # Check for zero volume (might be acceptable but worth noting)
            zero_volume_count = (data['Volume'] == 0).sum()
            if zero_volume_count > 0:
                logger.warning(f"Found {zero_volume_count} rows with zero volume")
    
    def prepare_for_backtesting(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Final preparation of data for backtesting.py library.
        
        This method ensures the data is in the exact format expected by
        backtesting.py's Backtest class.
        
        Args:
            data: Validated DataFrame with datetime index
            
        Returns:
            DataFrame ready for backtesting.py
        """
        # backtesting.py expects specific column order and types
        backtest_data = data.copy()
        
        # Ensure proper column order for backtesting.py
        column_order = ['Open', 'High', 'Low', 'Close', 'Volume']
        backtest_data = backtest_data[column_order]
        
        # Ensure proper data types
        for col in ['Open', 'High', 'Low', 'Close']:
            backtest_data[col] = backtest_data[col].astype(float)
        backtest_data['Volume'] = backtest_data['Volume'].astype(int)
        
        # Ensure datetime index is properly formatted
        if not isinstance(backtest_data.index, pd.DatetimeIndex):
            backtest_data.index = pd.to_datetime(backtest_data.index)
        
        logger.info(f"Data prepared for backtesting: {len(backtest_data)} rows, "
                   f"from {backtest_data.index.min()} to {backtest_data.index.max()}")
        
        return backtest_data
    
    def get_data_summary(self, data: pd.DataFrame) -> Dict:
        """
        Generate summary statistics for loaded data.
        
        Args:
            data: Market data DataFrame
            
        Returns:
            Dictionary with data summary statistics
        """
        return {
            'total_rows': len(data),
            'date_range': {
                'start': data.index.min().strftime('%Y-%m-%d %H:%M:%S'),
                'end': data.index.max().strftime('%Y-%m-%d %H:%M:%S')
            },
            'price_range': {
                'min_low': data['Low'].min(),
                'max_high': data['High'].max(),
                'avg_close': data['Close'].mean()
            },
            'volume_stats': {
                'total_volume': data['Volume'].sum(),
                'avg_volume': data['Volume'].mean(),
                'max_volume': data['Volume'].max()
            }
        }


# REMOVED: Wrapper function - Cortex will use DataIngestionManager() directly
