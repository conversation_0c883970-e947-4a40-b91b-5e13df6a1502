#!/usr/bin/env python3
"""
🎯 SITUATIONAL ANALYSIS PROMPTS

Simplified LLM prompts for situational pattern discovery.
Focuses on Python-compatible logic for reliable backtesting.
"""

class SituationalPrompts:
    """Generate situational analysis prompts for pattern discovery"""
    
    @staticmethod
    def get_core_situational_questions():
        """Core situational analysis questions that guide pattern discovery"""
        return [
            "When this market situation occurs, how do participants typically behave?",
            "What situational contexts create predictable behavioral responses?", 
            "Under what market conditions do statistical edges emerge from participant behavior?",
            "Why do certain market situations create measurable behavioral patterns?",
            "How do different participants react to similar market situations?",
            "What contextual factors create measurable behavioral responses?",
            "When do market inefficiencies emerge from situational dynamics?",
            "Under what conditions do participants behave predictably?"
        ]
    
    @staticmethod
    def get_tom_hougaard_examples():
        """<PERSON>'s ACTUAL situational analysis examples - ILLUSTRATIVE of thinking methodology"""
        return [
            "If one day is higher than the next, what does the following day look like? (Example: day-to-day momentum patterns)",
            "Is there evidence that what one session starts, another session continues? (Example: session continuation patterns)",
            "How often is a low or a high made in the first 30 minutes of the session?",
            "How often do gaps occur? Do they always fill, as the saying goes?",
            "How often does the market have trend days? How do you trade a trend day?",
            "Are there common denominators between strong trend days?",
            "Is there support that strong sessions lead to strong following sessions? (Example: momentum continuation)",
            "If one session is lower than the previous, what does that mean for the next session? (Example: reversal vs continuation)",
            "When volatility regime changes, how do participants behave differently?",
            "Under what session conditions do behavioral opportunities emerge?",
            "What participant interactions create statistical edges during specific market contexts?",
            "When do institutional vs retail behaviors create exploitable patterns?"
        ]
    
    @staticmethod
    def generate_discovery_prompt(ohlc_data, profit_context="", market_summaries="", performance_feedback=""):
        """
        Generate situational analysis prompt for pattern discovery
        Focuses on Python-compatible logic for reliable backtesting
        """
        
        # Enforce strict OHLCV capitalization and fail-fast if missing
        required_cols = ['Open', 'High', 'Low', 'Close']
        missing = [col for col in required_cols if col not in ohlc_data.columns]
        if missing:
            raise RuntimeError(f'UNBREAKABLE RULE VIOLATION: Missing OHLC columns: {missing}')

        core_questions = SituationalPrompts.get_core_situational_questions()
        tom_examples = SituationalPrompts.get_tom_hougaard_examples()

        # CRITICAL: Add market regime analysis to guide pattern discovery
        regime_analysis = SituationalPrompts._analyze_market_regime(ohlc_data)

        prompt = f"""You are an expert Python trader discovering SIMPLE trading patterns for backtesting analysis.

 GENERATE PATTERNS AS PYTHON-READY LOGIC:
Create patterns using simple Python conditions that execute frequently and can be easily backtested.

 TRADING COSTS TO CONSIDER:
💰 TRADING COSTS TO CONSIDER:
- Spread: 1 pip (0.0001) - This is the cost of entering/exiting trades
- Commission: None
- Your patterns MUST account for this 1-pip spread cost to remain profitable
- Ensure profit targets are sufficient to overcome the 1-pip spread

🎯 MARKET ORDER EXECUTION:
- System uses MARKET ORDERS with immediate stop loss and take profit
- No minimum distance restrictions (realistic for actual trading)
- Focus on profitable patterns, not artificial distance constraints

🚨 CRITICAL CONSTRAINT: Patterns must be simple text descriptions, NOT Python code.

🚨 MANDATORY SYNTAX - EXACT FORMAT REQUIRED:
Entry Logic: current_close > previous_high  [EXACT SYNTAX - NO DESCRIPTIVE TEXT]
Stop Logic: previous_low  [EXACT SYNTAX - NO DESCRIPTIVE TEXT]
Target Logic: entry_price + (entry_price - stop_price) * 1.5  [EXACT SYNTAX]
Direction: long  [MUST BE: long OR short]
Position Size: 1.0  [MUST BE: numeric value]

⚠️ CRITICAL: Use ONLY these exact entry logic formats:
- current_close > previous_high
- current_close < previous_low
- current_close > previous_close
- current_close < previous_close
- current_high > previous_high
- current_low < previous_low

⚠️ CRITICAL: Use ONLY these exact stop logic formats:
- previous_low
- previous_high
- previous_close

STOP LOSS EXAMPLES (SIMPLE):
- For LONG trades: previous_low, current_low, or percentage-based
- For SHORT trades: previous_high, current_high, or percentage-based
- Keep it simple - market orders handle any distance automatically

🔍 ENHANCED BEHAVIORAL ANALYSIS:
{regime_analysis}

{market_summaries}

📚 LEARNING FROM PREVIOUS SESSIONS:
{performance_feedback}

🎯 REQUIRED OUTPUT FORMAT - PROFITABLE BACKTESTING PATTERNS:

### 🚨 PROFITABILITY REQUIREMENT - MANDATORY FOR FILE GENERATION 🚨
⚠️ CRITICAL: Files are ONLY generated for PROFITABLE patterns!
🎯 MANDATORY PROFITABILITY CRITERIA - ALL PATTERNS MUST MEET THESE:
- Profit target MUST be at least 3x stop distance (3:1 risk-reward minimum)
- Pattern must align with the market regime analysis provided above
- Must overcome 1-pip spread cost PLUS generate substantial profits
- Focus ONLY on HIGH-PROBABILITY setups with clear statistical edges
- Expected win rate >65% OR risk-reward ratio >3:1
- NO GENERIC BREAKOUT PATTERNS - they typically lose money
- Patterns must exploit specific behavioral inefficiencies

### PATTERN DIRECTION GUIDELINES:
- Discover patterns for BOTH directions: LONG and SHORT
- LONG patterns: Entry above previous levels, Stop below entry, Target above entry
- SHORT patterns: Entry below previous levels, Stop above entry, Target below entry
- Ensure stop loss and target are logically consistent with direction

### RULE CLARITY REQUIREMENT

Discover 3-5 PROFITABLE patterns using EXACTLY this text format (NO Python code):

**PATTERN 1: [Sophisticated Pattern Name]**
Market Logic: [Explain specific behavioral inefficiency this exploits - WHY participants behave predictably in this situation]
Entry Logic: current_close > previous_high
Direction: long
Stop Logic: previous_low
Target Logic: entry_price + (entry_price - stop_price) * 3.0
Position Size: 1.0
Timeframe: 5min

**PATTERN 2: [Counter-Trend or Mean Reversion Pattern]**
Market Logic: [Explain why this specific situation creates profitable reversals - participant psychology]
Entry Logic: current_close < previous_low
Direction: short
Stop Logic: previous_high
Target Logic: entry_price - (stop_price - entry_price) * 3.0
Position Size: 1.0
Timeframe: 5min

**PATTERN 3: [Another Pattern]**
[Continue same format for additional patterns...]

🚨 CRITICAL SYNTAX REQUIREMENTS - MANDATORY:
- Entry Logic MUST use EXACT syntax from approved list above
- DO NOT use descriptive text like "Entry above previous high on strong trend day"
- DO NOT add extra conditions or qualifiers to entry logic
- USE ONLY the exact formats: current_close > previous_high, etc.
- Stop Logic MUST be: previous_low, previous_high, or previous_close
- Target Logic MUST use exact format: entry_price + (entry_price - stop_price) * X.X

🚨 PROFITABILITY REQUIREMENTS:
- AVOID patterns that ignore market regime - align with trend direction provided above
- USE wide enough targets (3:1 minimum) to overcome spread costs
- FOCUS on patterns that exploit specific participant behavioral biases
- ENSURE each pattern has a clear statistical edge based on situational analysis

PATTERN DISCOVERY vs RULE EXECUTION:
- USE behavioral intelligence, regime analysis for PATTERN DISCOVERY
- TRANSLATE sophisticated insights into SIMPLE Python-compatible execution rules
- Behavioral intelligence helps you FIND patterns, but rules must be Python-simple

EXECUTION RULE REQUIREMENTS - BACKTESTING COMPATIBLE:
- ALL RULES MUST BE SIMPLE PYTHON LOGIC
- Use basic price comparisons: current_close > previous_high
- Time filters: Simple hour checks if needed
- Position sizing: Absolute values (1.0, 2.0, etc.) - NO percentages
- Entry conditions: Simple price comparisons
- Stop loss: Previous High/Low or simple calculations
- Exit: Simple profit targets or time-based
- NO complex formulas, NO special syntax, NO complex calculations
- Keep all logic simple and Python-compatible for reliable backtesting

CORE SITUATIONAL QUESTIONS TO EXPLORE:
{chr(10).join([f'- "{q}"' for q in core_questions[:4]])}

SITUATIONAL ANALYSIS EXAMPLES (Tom Hougaard methodology):
These are EXAMPLES of situational thinking - adapt the CONCEPT, not the specific days:
{chr(10).join([f'- "{example}"' for example in tom_examples[:6]])}

⚠️ IMPORTANT: These examples show the TYPE of situational thinking to use.
   Do NOT create rigid rules tied to specific days of the week.
   Instead, look for SITUATIONAL PATTERNS that work across different time contexts.

🚨 FORMAT REQUIREMENT:
   - Use SIMPLE TEXT format as shown in examples above
   - DO NOT write Python functions or code blocks
   - DO NOT use ```python or any code syntax
   - Use plain text pattern descriptions only"""

        return prompt

    @staticmethod
    def _analyze_market_regime(ohlc_data):
        """Analyze market regime for intelligent pattern discovery"""
        try:
            # Get price data
            close_col = ohlc_data.get('close', ohlc_data.get('Close', ohlc_data.iloc[:, -2]))

            # Calculate trend
            start_price = close_col.iloc[0]
            end_price = close_col.iloc[-1]
            total_return = ((end_price - start_price) / start_price) * 100

            # Calculate volatility
            returns = close_col.pct_change().dropna()
            volatility = returns.std() * 100

            # Determine regime and bias
            if total_return > 2:
                regime = "STRONG UPTREND"
                bias = "🎯 FOCUS ON LONG PATTERNS - Avoid shorts against strong trend"
                pattern_guidance = "Prioritize breakout patterns, momentum continuation, pullback entries"
            elif total_return > 0.5:
                regime = "UPTRENDING"
                bias = "📈 FAVOR LONG PATTERNS - Be selective with shorts"
                pattern_guidance = "Use trend-following patterns, avoid aggressive counter-trend trades"
            elif total_return < -2:
                regime = "STRONG DOWNTREND"
                bias = "🎯 FOCUS ON SHORT PATTERNS - Avoid longs against strong trend"
                pattern_guidance = "Prioritize breakdown patterns, momentum continuation, bounce shorts"
            elif total_return < -0.5:
                regime = "DOWNTRENDING"
                bias = "📉 FAVOR SHORT PATTERNS - Be selective with longs"
                pattern_guidance = "Use trend-following patterns, avoid aggressive counter-trend trades"
            else:
                regime = "RANGING"
                bias = "⚖️ BALANCED APPROACH - Use both directions with mean reversion focus"
                pattern_guidance = "Focus on range-bound patterns, support/resistance bounces"

            return f"""
📊 Market Regime: {regime} ({total_return:+.2f}% total return)
📊 Volatility Level: {volatility:.2f}% (daily price movement)
{bias}
🧠 Pattern Strategy: {pattern_guidance}

⚠️ CRITICAL: Align your pattern discovery with this regime analysis.
   Patterns that fight the dominant trend will likely be unprofitable."""

        except Exception as e:
            return f"Market regime analysis unavailable: {e}"
